# 帮我写一个shell 脚本，监听8080，当有连接时，打印连接信息（包括客户端ip和端口），并返回一个hello world，当有数据时，打印数据，并返回一个 chat ，当连接关闭时，打印关闭信息，并返回一个close

#!/bin/bash

# Create a named pipe for communication
PIPE=/tmp/chatpipe
mkfifo $PIPE 2>/dev/null

# Clean up pipe on exit
trap "rm -f $PIPE" EXIT

# Start netcat to listen on port 8080
while true; do
    nc -l 8080 < $PIPE | while read line; do
        # Get client IP and port
        client_info=$(netstat -n | grep :8080 | grep ESTABLISHED | awk '{print $5}')
        
        if [ -z "$line" ]; then
            # Connection closed
            echo "Connection closed from $client_info"
            echo "close" > $PIPE
            break
        else
            # New connection or data received
            if [ "$line" == "" ]; then
                echo "New connection from $client_info"
                echo "hello world" > $PIPE
            else
                echo "Received data from $client_info: $line"
                echo "chat" > $PIPE
            fi
        fi
    done
done
