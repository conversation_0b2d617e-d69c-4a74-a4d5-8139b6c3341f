# zookeeper.yaml

# 1. ConfigMap: 包含 zoo.cfg 模板和启动脚本
apiVersion: v1
kind: ConfigMap
metadata:
  name: zk-config
  labels:
    app: zookeeper
data:
  # run.sh: 智能启动脚本
  run.sh: |
    #!/bin/bash
    
    # 设置严格模式
    set -e
    
    # 从 StatefulSet 的 Pod 名称中提取序号 (例如 zk-0 -> 0)
    # 使用正则表达式从主机名中提取末尾的数字
    # sed 's/.*-\([0-9]\+\)/\1/' 会匹配到最后一个连字符后的数字
    ORDINAL=${HOSTNAME##*-}
    
    # ZooKeeper 的 myid 必须是从 1 开始的整数
    MYID=$((ORDINAL + 1))
    
    # 定义数据目录和 myid 文件路径
    DATA_DIR="/data"
    MYID_FILE="$DATA_DIR/myid"
    
    # 创建数据目录（如果不存在）
    mkdir -p $DATA_DIR
    
    # 将计算出的 myid 写入文件
    echo "Writing myid: $MYID to $MYID_FILE"
    echo $MYID > $MYID_FILE
    
    # 生成 zoo.cfg 配置文件
    CONFIG_FILE="/conf/zoo.cfg"
    echo "Generating ZooKeeper config file: $CONFIG_FILE"
    
    # 写入基础配置
    cat > $CONFIG_FILE <<EOF
    tickTime=2000
    initLimit=10
    syncLimit=5
    dataDir=${DATA_DIR}
    clientPort=2181
    autopurge.snapRetainCount=3
    autopurge.purgeInterval=1
    4lw.commands.whitelist=*
    EOF
    
    # 动态生成 server 列表
    # 使用 Headless Service 的 DNS 名称进行节点发现
    # Replicas=3，所以循环 3 次
    for i in $(seq 0 2); do
      echo "server.$((i + 1))=zk-${i}.zk-headless.default.svc.cluster.local:2888:3888" >> $CONFIG_FILE
    done
    
    echo "Generated zoo.cfg:"
    cat $CONFIG_FILE
    
    # 以前台模式启动 ZooKeeper 服务器
    # 使用我们刚刚生成的配置文件
    echo "Starting ZooKeeper..."
    exec zkServer.sh start-foreground

---
# 2. Headless Service: 用于节点间通信和发现
apiVersion: v1
kind: Service
metadata:
  name: zk-headless
  labels:
    app: zookeeper
spec:
  # clusterIP: None 表示这是一个 Headless Service
  # 它不会分配虚拟 IP，而是直接暴露 Pod 的 IP 地址
  clusterIP: None
  ports:
    - name: peer
      port: 2888
      protocol: TCP
    - name: leader-election
      port: 3888
      protocol: TCP
  selector:
    app: zookeeper

---
# 3. Client Service: 用于客户端访问
apiVersion: v1
kind: Service
metadata:
  name: zk-client
  labels:
    app: zookeeper
spec:
  # 默认是 ClusterIP 类型
  type: ClusterIP
  ports:
    - name: client
      port: 2181
      protocol: TCP
  # 将流量转发到所有带有 app=zookeeper 标签的 Pod
  selector:
    app: zookeeper

---
# 4. PodDisruptionBudget: 保证高可用
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: zk-pdb
  labels:
    app: zookeeper
spec:
  # 对于一个 3 节点的集群，minAvailable: 2 意味着
  # 在任何时候都必须至少有 2 个 Pod 处于可用状态，
  # 这样可以维持法定人数 (Quorum)。
  minAvailable: 2
  selector:
    matchLabels:
      app: zookeeper

---
# 5. StatefulSet: 部署和管理 ZooKeeper Pod
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: zk
  labels:
    app: zookeeper
spec:
  # serviceName 必须与 Headless Service 的名称匹配
  # 这是 StatefulSet 为其 Pod 创建稳定 DNS 名称的基础
  serviceName: "zk-headless"
  replicas: 3
  # podManagementPolicy: Parallel 会同时创建所有 Pod，启动速度更快
  # OrderedReady (默认) 会一个一个地创建并等待其就绪
  podManagementPolicy: Parallel
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      # 在某些环境下，特别是使用 hostNetwork 时，需要设置 DNS 策略
      # dnsPolicy: ClusterFirstWithHostNet
      terminationGracePeriodSeconds: 10
      containers:
        - name: zookeeper
          # 使用一个官方且稳定的 ZooKeeper 镜像
          image: zookeeper:3.8
          ports:
            - name: client
              containerPort: 2181
            - name: peer
              containerPort: 2888
            - name: leader-election
              containerPort: 3888
          # 容器启动时执行 ConfigMap 中的脚本
          command: ["/bin/bash", "-c", "/conf/run.sh"]
          # 健康检查
          readinessProbe:
            exec:
              command:
                - /bin/bash
                - -c
                - "echo stat | nc localhost 2181 | grep Mode"
            initialDelaySeconds: 10
            timeoutSeconds: 5
          livenessProbe:
            exec:
              command:
                - /bin/bash
                - -c
                - "echo ruok | nc localhost 2181 | grep imok"
            initialDelaySeconds: 10
            timeoutSeconds: 5
          volumeMounts:
            # 挂载持久化存储卷到 /data 目录
            - name: data
              mountPath: /data
            # 挂载 ConfigMap 到 /conf 目录
            - name: zk-config-volume
              mountPath: /conf
      volumes:
        # 定义一个卷来引用 ConfigMap
        - name: zk-config-volume
          configMap:
            name: zk-config
            # 将 run.sh 脚本设置为可执行
            defaultMode: 0755
            items:
              - key: run.sh
                path: run.sh
  # volumeClaimTemplates: PVC 模板，将为每个 Pod 自动创建 PVC
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        # **重要**: 请将 "your-storage-class" 替换为你的 K8s 集群中
        # 实际可用的 StorageClass 名称。
        # 你可以通过 `kubectl get sc` 命令查看。
        storageClassName: "your-storage-class"
        resources:
          requests:
            storage: 2Gi