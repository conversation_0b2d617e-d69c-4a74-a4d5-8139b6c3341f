#场景一：使用 volumeBindingMode: Immediate (默认模式)
#如果你的StorageClass没有设置volumeBindingMode，或者设置为Immediate，那么流程会是这样：
#
#
# 1. 你apply了StatefulSet的YAML文件。
# 2. StatefulSet控制器立刻创建出3个PVC：data-zk-0, data-zk-1, data-zk-2。
# 3. Kubernetes的PV控制器立即扫描所有可用的PV，寻找能满足这3个PVC要求的PV（匹配storageClassName、accessModes和resources）。
# 4. 它找到了你创建的pv-zk-0, pv-zk-1, pv-zk-2。于是立刻将它们分别绑定到3个PVC上。此时，PVC的状态变为Bound。
# 5. 然后，StatefulSet控制器开始创建Pod zk-0。
# 6. 调度器在为zk-0选择节点时，发现它需要使用PVC data-zk-0，而这个PVC已经绑定到了pv-zk-0上。pv-zk-0的nodeAffinity规定了它必须在minikube节点上。因此，调度器别无选择，只能将zk-0调度到minikube节点。
#
#
#问题：在多节点环境中，这种模式可能导致调度失败。比如，pv-zk-0在node-A上，但调度器因为资源紧张等原因想把zk-0调度到node-B，这是不可能的，因为存储已经被“锁死”在了node-A。
#
#---
#
#场景二：使用 volumeBindingMode: WaitForFirstConsumer (你的配置)
#
#这是你当前zookeeper-pv.yaml中的配置，流程完全不同：
#
#
# 1. 你apply了StatefulSet的YAML文件。
# 2. StatefulSet控制器立刻创建出3个PVC：data-zk-0, data-zk-1, data-zk-2。
# 3. 关键区别：由于设置了WaitForFirstConsumer，PV控制器不会立即进行绑定。这3个PVC会一直处于Pending（等待中）状态。它们在等待第一个“消费者”（也就是使用它们的Pod）出现。
# 4. StatefulSet控制器开始创建Pod zk-0。
# 5. 调度器准备调度zk-0。它知道zk-0需要一个满足data-zk-0这个PVC要求的卷。
# 6. 现在，调度器会同时考虑Pod的调度需求和PV的限制。它会寻找一个节点，这个节点既能满足Pod的运行要求（CPU、内存等），也能满足其关联的PV的nodeAffinity要求。
# 7. 在你的例子中，调度器看到pv-zk-0, pv-zk-1, pv-zk-2都在minikube节点上。于是它做出决定：将zk-0调度到minikube节点。
# 8. 在Pod被成功调度到节点之后，PV控制器才会将data-zk-0与一个位于minikube节点上的可用PV（即pv-zk-0）进行绑定。
# 9. 这个过程会为zk-1和zk-2依次重复。
#


apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage-zk
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-zk-0
  labels:
    type: local
    app: zookeeper
spec:
  storageClassName: "local-storage-zk"
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  local:
    # This path must exist on the node defined in nodeAffinity
    path: /mnt/zk-0
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          # Replace with your actual node name
          - minikube
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-zk-1
  labels:
    type: local
    app: zookeeper
spec:
  storageClassName: "local-storage-zk"
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  local:
    # This path must exist on the node defined in nodeAffinity
    path: /mnt/zk-1
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          # Replace with your actual node name
          - minikube
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-zk-2
  labels:
    type: local
    app: zookeeper
spec:
  storageClassName: "local-storage-zk"
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  local:
    # This path must exist on the node defined in nodeAffinity
    path: /mnt/zk-2
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          # Replace with your actual node name
          - minikube
