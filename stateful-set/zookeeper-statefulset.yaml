apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: zk
spec:
  selector:
    matchLabels:
      app: zk
  # serviceName 指定 StatefulSet 所关联的 Headless Service 名称。这里用 "zk-headless" 而不是 "zk"，是因为 ZooKeeper 集群需要每个 Pod 都有一个稳定的 DNS 域名用于相互通信和选举，
  #而 Headless Service（clusterIP: None）会为每个 Pod 分配唯一的 DNS 记录，满足这一需求。如果用普通 Service（如 "zk"），则所有 Pod 共享一个虚拟 IP，无法实现节点间的直接寻址。
  serviceName: "zk-headless"
  replicas: 3
  template:
    metadata:
      labels:
        app: zk
    spec:
      terminationGracePeriodSeconds: 60
      # securityContext.fsGroup: 这里设置 fsGroup: 1000，表示 Pod 内所有容器挂载的卷（如 PersistentVolume）所属的文件/目录的 GID 会被设置为 1000。
      # 这样，容器内运行的进程（即使不是 root 用户）也能以组 1000 的权限访问这些卷上的文件，常用于解决挂载卷的权限问题。
      # 
      # 注意：这个 securityContext 是 Pod 级别的，作用于整个 Pod 的所有容器挂载的卷。
      # 如果你在 containers 下的 securityContext 里设置 runAsUser: 1000，则容器内主进程会以 UID 1000 运行。
      # 而 fsGroup 只影响卷的 GID，不影响进程的 UID。
      securityContext: 
        fsGroup: 1000
      containers:
      - name: zookeeper
        image: zookeeper:3.8
        ports:
        - containerPort: 2181
          name: client
        - containerPort: 2888
          name: server
        - containerPort: 3888
          name: leader-election
        command:
        - "/bin/bash"
        - "-c"
        - "/mnt/config/startup.sh"
        volumeMounts:
        - name: data
          mountPath: /data
        - name: config-volume
          mountPath: /mnt/config
      volumes:
      - name: config-volume
        configMap:
          name: zk-cm
          defaultMode: 0755 # 0755 表示拥有者有读、写、执行权限，组和其他用户有读、执行权限（rwxr-xr-x），常用于脚本或可执行文件
  # 注意：StatefulSet 的 volumeClaimTemplates 字段一旦创建后，**大多数字段（如 storageClassName、accessModes、name）都不可变**。
  # 如果你 apply 之后再修改 storageClassName（比如从 "local-storage-zk" 改为 "another-sc"），Kubernetes 会报错，提示 volumeClaimTemplates 字段不可变，无法直接更新。
  # 你会看到类似错误：
  #   spec.volumeClaimTemplates field is immutable
  # 解决方法通常是：删除 StatefulSet（保留 PVC），然后用新配置重新创建 StatefulSet，或者更换 StatefulSet 名称。
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "local-storage-zk"
      resources:
        requests:
          storage: 1Gi # Adjust storage size as needed
