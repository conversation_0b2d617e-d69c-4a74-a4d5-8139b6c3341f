apiVersion: v1
kind: ConfigMap
metadata:
  name: zk-cm
data:
  # The startup script that dynamically generates the myid
  startup.sh: |
    #!/bin/bash
    set -e
    DATA_DIR=/data
    MYID_FILE=$DATA_DIR/myid
    # Ensure the data directory exists
    mkdir -p $DATA_DIR
    # Get the ordinal index from the hostname
    ORDINAL=${HOSTNAME##*-}
    # Convert ordinal to myid (starts from 1)
    MYID=$((ORDINAL + 1))
    echo $MYID > $MYID_FILE
    # Start ZooKeeper using the official entrypoint
    /docker-entrypoint.sh zkServer.sh start-foreground

  # The zoo.cfg configuration file
  zoo.cfg: |
    tickTime=2000
    dataDir=/data
    clientPort=2181
    initLimit=10
    syncLimit=5
    # Use stable DNS names provided by the Headless Service for server discovery
    server.1=zk-0.zk-headless.default.svc.cluster.local:2888:3888
    server.2=zk-1.zk-headless.default.svc.cluster.local:2888:3888
    server.3=zk-2.zk-headless.default.svc.cluster.local:2888:3888
