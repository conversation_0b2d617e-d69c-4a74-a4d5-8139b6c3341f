apiVersion: v1
kind: PersistentVolume
metadata:
  name: grafana-pv
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: /nas/k8s/grafana
    server: *************   # ✅ 替换成你的 NAS IP
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""  # 不使用动态卷时建议设为空
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: default
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  selector:
    matchLabels:
      pv-type: grafana
  storageClassName: ""  # 与上面保持一致
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana-deployment
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
        - name: grafana
          image: your-java-app:latest
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          volumeMounts:
            - name: grafana-storage
              mountPath: /opt/production/webapp
              subPathExpr: $(POD_NAME)
          command: ["java"]
          args:
            - "-XX:ErrorFile=/opt/production/webapp/hs_err_%p_%t.log"
            - "-jar"
            - "app.jar"
      volumes:
        - name: grafana-storage
          persistentVolumeClaim:
            claimName: grafana-pvc
