{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Operator (go run main.go)",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/operator/redis-cluster/kubebuilder-project/cmd/main.go",
      "args": [
        "--leader-elect"  // 可选，加了也不会冲突
      ],
      "env": {
        "KUBEBUILDER_ASSETS": "/path/to/kubebuilder/bin",  // 如果需要测试 webhook，可能需要
        "WATCH_NAMESPACE": "default"  // 也可以配置你要监听的 namespace
      }
    }
  ]
}
// 运行方法:
// 1. 在 VS Code 中按 F5 或点击调试按钮启动调试
// 2. 确保已安装 Go 扩展
// 3. 确保 kubeconfig 配置正确且可以访问集群
// 4. 如果需要 webhook 功能,需要正确配置 KUBEBUILDER_ASSETS 环境变量
