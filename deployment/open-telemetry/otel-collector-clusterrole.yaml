apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: otel-collector-agent-role
  labels:
    app: opentelemetry
    component: otel-collector-agent-role
rules:
  - apiGroups: [""]
    resources:
      - "pods"
      - "nodes"
      - "nodes/proxy" # k8sattributesprocessor 可能需要访问节点代理以获取更丰富的节点信息
      - "nodes/stats"
      - "namespaces"
      - "services" # 如果需要监控服务
      - "endpoints" # 如果需要监控端点
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources:
      - "deployments"
      - "replicasets"
      - "daemonsets"
      - "statefulsets"
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"] # 对于较旧的K8s版本
    resources:
      - "deployments"
      - "replicasets"
      - "daemonsets"
    verbs: ["get", "list", "watch"]
  - nonResourceURLs: ["/metrics", "/metrics/cadvisor"] # 如果需要从Kubelet /metrics/cadvisor端点抓取
    verbs: ["get"]