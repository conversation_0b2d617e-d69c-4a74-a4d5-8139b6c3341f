apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-collector-config
  namespace: monitoring
data:
  otel-collector-config.yaml: |
    receivers:
      hostmetrics:
        root_path: /hostfs
        collection_interval: 30s
        scrapers:
          memory:
          process:
            include:
              match_type: regexp
              names:
                - ".*"  # 采集所有进程
    processors:
      memory_limiter:
        check_interval: 1s
        limit_percentage: 75 # 基于容器的内存限制
        spike_limit_percentage: 15
      batch:
        timeout: 10s
      # 重点: k8sattributes 用于添加 Kubernetes 元数据标签
      k8sattributes:
        auth_type: "serviceAccount" # 使用Pod的ServiceAccount与K8s API交互
        passthrough: false # 不传递原始的非k8s标签，如云提供商标签
        filter:
          node_from_env_var: KUBE_NODE_NAME # 从环境变量获取节点名，更可靠
        extract:
          metadata: # 要添加到指标的元数据标签
            - k8s.pod.name
            - k8s.pod.uid
            - k8s.deployment.name
            - k8s.namespace.name
            - k8s.node.name
            - k8s.container.name # 重要：将进程关联到容器
            # - k8s.replicaset.name
            # - k8s.daemonset.name
            # - k8s.statefulset.name
        pod_association: # 如何将非Pod资源（如主机进程）关联到Pod
          - sources:
              - from: cgroup # 通过cgroup信息关联进程到容器/Pod

    exporters:
      prometheus:
        endpoint: "0.0.0.0:8889"

    service:
      pipelines:
        metrics:
          receivers: [hostmetrics]
          processors: [ batch, k8sattributes]
          exporters: [prometheus]
