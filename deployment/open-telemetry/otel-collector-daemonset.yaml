apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: otel-collector-agent
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: otel-collector-agent
  template:
    metadata:
      labels:
        app: otel-collector-agent
    spec:
      serviceAccountName: otel-collector-agent
      hostPID: true
      containers:
        - name: otel-collector
          image: otel/opentelemetry-collector-contrib:0.100.0
          args:
            - "--config=/conf/otel-collector-config.yaml"
          ports:
            - containerPort: 8889
              name: metrics
          securityContext:
            runAsUser: 0
            privileged: true
          volumeMounts:
            - name: config
              mountPath: /conf
            #- name: proc
            #  mountPath: /host/proc
            #  readOnly: true
            ## 重点: 挂载宿主机的cgroup文件系统
            #- name: host-sys-fs-cgroup
            #  mountPath: /sys/fs/cgroup
            #  readOnly: true    
            - name: host-passwd # 引用上面定义的卷名称
              mountPath: /etc/passwd # 在容器内部挂载到 /etc/passwd
              readOnly: true # 设为只读，Collector只需要读取
            - name: hostfs
              mountPath: /hostfs
              readOnly: true
              mountPropagation: HostToContainer
          env:
            # 重点: 将节点名作为环境变量传递给k8sattributesprocessor
            - name: KUBE_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            # 可选：Pod IP, Name, Namespace，用于Collector自身指标的丰富化
            - name: K8S_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
      volumes:
        - name: config
          configMap:
            name: otel-collector-config
        - name: proc
          hostPath:
            path: /proc
            type: Directory
        - name: hostfs
          hostPath:
            path: /
            type: Directory
        - name: host-passwd # 给这个卷起个名字
          hostPath:
            path: /etc/passwd # 宿主机上 /etc/passwd 的路径
            type: File        # 说明这是一个文件类型 (可选，但明确)
        - name: host-sys-fs-cgroup
          hostPath:
            path: /sys/fs/cgroup
            type: Directory