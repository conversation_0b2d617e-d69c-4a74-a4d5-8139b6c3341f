apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: otel-collector-agent-binding
  labels:
    app: opentelemetry
    component: otel-collector-agent-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: otel-collector-agent-role
subjects:
  - kind: ServiceAccount
    name: otel-collector-agent
    namespace: monitoring # 与ServiceAccount定义的命名空间一致