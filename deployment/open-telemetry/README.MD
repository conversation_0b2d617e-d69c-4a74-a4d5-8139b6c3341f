kubectl get daemonsets.apps
kubectl apply -f otel-collector-config.yaml
kubectl delete daemonsets.apps otel-collector-agent 
kubectl apply -f otel-collector-daemonset.yaml
sleep 5
kubectl get pods 
kubectl logs `kubectl get pods -l app=otel-collector-agent --no-headers | awk '{print $1}'`





wget http://192.168.49.2:32089/metrics

rm -f metrics
wget http://10.244.0.143:8889/metrics

32089
kubectl port-forward svc/otel-collector-nodeport 8889:8889


