global:
  scrape_interval:     60s
  evaluation_interval: 60s
 
scrape_configs:
  - job_name: prometheus
    static_configs:
      - targets: ['localhost:9090']
        labels:
          instance: prometheus
 
  - job_name: linux
    static_configs:
      - targets: ['************:9100']
        labels:
          instance: ************
  - job_name: 'kubernetes-cadvisor'
    kubernetes_sd_configs:
      - role: node
        api_server: http://host.docker.internal:8001 #  kubectl proxy --address='0.0.0.0' --disable-filter=true &    kubectl proxy 配置一个本地代理
        bearer_token_file: /etc/prometheus/token # 挂载的 token 文件路径
        tls_config:
          ca_file: /etc/prometheus/ca.crt # 挂载的 CA 证书文件路径
    relabel_configs:
      - source_labels: [__metrics_path__]
        action: replace
        target_label: __metrics_path__
        replacement: /metrics
      - source_labels: [__address__]
        action: replace
        target_label: __address__
        regex: (.*):\d+
        replacement: '${1}:32080'
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)