docker build -t my-prometheus:v10 .               
minikube image load my-prometheus:v10



kubectl delete deployments prometheus
kubectl apply -f prometheus-deployment.yaml
sleep 5
kubectl get pods 
kubectl logs `kubectl get pods -l app=prometheus --no-headers | awk '{print $1}'`



kubectl get daemonsets.apps
kubectl delete daemonsets.apps cadvisor 
kubectl apply -f cadvisor-daemonset.yaml
sleep 5
kubectl get pods 



源机器->机房路由器->企业出口光猫-> slb-> 内网:2181

# 将请求按比例分配到不同的目标地址
iptables -t nat -A PREROUTING -p tcp --dport 2181 -m statistic --mode nth --every 3 --packet 0 -j DNAT --to-destination *************:2181
iptables -t nat -A PREROUTING -p tcp --dport 2181 -m statistic --mode nth --every 2 --packet 0 -j DNAT --to-destination *************:2181
iptables -t nat -A PREROUTING -p tcp --dport 2181 -m statistic --mode nth --every 1 --packet 0 -j DNAT --to-destination *************:2181

# 保持原始客户端IP
iptables -t nat -A POSTROUTING -p tcp --dport 2181 -j SNAT --to-source оригинальный_src_ip # This should be more specific


-A PREROUTING -d **************/32 -p tcp --dport 2181 -j KUBE-SVC-ORDER-SERVICE
-A KUBE-SVC-ORDER-SERVICE -m statistic --mode random --probability 0.5 -j KUBE-SEP-ORDER-C
-A KUBE-SVC-ORDER-SERVICE -m statistic --mode random --probability 0.5 -j KUBE-SEP-ORDER-D
-A KUBE-SEP-ORDER-C -s ************/32 -j MARK --set-xmark 0x2000/0x2000
-A KUBE-SEP-ORDER-C -j DNAT --to-destination ************:8080
-A KUBE-SEP-ORDER-D -s ************/32 -j MARK --set-xmark 0x2000/0x2000
-A KUBE-SEP-ORDER-D -j DNAT --to-destination ************:8080 





-Dotel.resource.attributes=service.name=pigeon-remote,token=AutISSJRiRSTERSYWEWD -Dotel.exporter.otlp.endpoint=http://ap-shanghai.apm.tencentcs.com:4317 -Ddubbo.protocol.dubbo.threads=10 -Darch.mq.consumer.enabled=false -Dsms.callback.query.beans.normal= -Dsms.callback.query.beans.market=dreamNetSmsChannelService -Dnotice.sync.process=true -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xmx2048m -Xms2048m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/ -XX:MaxMetaspaceSize=384m -Xloggc:/app/logs/gc.log