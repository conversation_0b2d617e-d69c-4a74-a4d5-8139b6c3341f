global:
  scrape_interval:     60s
  evaluation_interval: 60s
 
scrape_configs:
  - job_name: prometheus
    static_configs:
      - targets: ['localhost:9090']
        labels:
          instance: prometheus
 
  - job_name: linux
    static_configs:
      - targets: ['************:9100']
        labels:
          instance: ************
  - job_name: 'kubernetes-cadvisor'
    kubernetes_sd_configs:
      - role: node
        api_server: https://kubernetes.default.svc:443
        #bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token # 自动挂载的 token 文件路径
        #tls_config:
          #ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt # 自动挂载的 CA 证书文件路径
        bearer_token_file: /etc/prometheus/token # 挂载的 token 文件路径
        tls_config:
          ca_file: /etc/prometheus/ca.crt # 挂载的 CA 证书文件路径
    relabel_configs:
      - source_labels: [__metrics_path__]
        action: replace
        target_label: __metrics_path__
        replacement: /metrics
      - source_labels: [__address__]
        action: replace
        target_label: __address__
        regex: (.*):\d+
        replacement: '${1}:32080'
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
  - job_name: 'kubernetes-otel-collector'
    kubernetes_sd_configs:
      - role: node
        api_server: https://kubernetes.default.svc:443
        bearer_token_file: /etc/prometheus/token
        tls_config:
          ca_file: /etc/prometheus/ca.crt
    relabel_configs:
      - source_labels: [__metrics_path__]
        action: replace
        target_label: __metrics_path__
        replacement: /metrics
      - source_labels: [__address__]
        action: replace
        target_label: __address__
        regex: (.*):\d+
        replacement: '${1}:32089'
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)

