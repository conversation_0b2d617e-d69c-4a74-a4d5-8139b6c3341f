FROM prom/prometheus

# 复制 Prometheus 配置文件
COPY prometheus.yml /etc/prometheus/prometheus.yml

# 复制 CA 证书文件
COPY ca.crt /etc/prometheus/ca.crt

# 复制 Kubernetes token 文件
COPY k8s-prometheus-token.txt /etc/prometheus/token

# 创建数据目录并设置合适的权限 ,继承了prom/prometheus ，不需要指定
#RUN mkdir -p /prometheus && \
#    chown -R nobody:nogroup /prometheus && \
#    chmod -R 775 /prometheus
#
# 指定工作目录
#WORKDIR /etc/prometheus

# 暴露 Prometheus 端口
# EXPOSE 9090

# 启动 Prometheus 继承了prom/prometheus ，不需要指定
#CMD [ "--config.file=/etc/prometheus/prometheus.yml", \
#      "--storage.tsdb.path=/prometheus" ]