apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  namespace: app-namespace # 确保 namespace 与您应用一致，如果不同请修改
  labels:
    app: nginx
spec:
  replicas: 1 #  可以根据需要调整副本数量
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx-container
        image: nginx:latest # 使用官方 nginx 镜像
        ports:
        - containerPort: 80 # 容器内部端口 80
        resources: #  资源限制 (可选，但推荐)
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "250m"
            memory: "256Mi"
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
  namespace: app-namespace # 确保 namespace 与 Deployment 一致，如果不同请修改
spec:
  selector:
    app: nginx
  ports:
  - protocol: TCP
    port: 80 # Service 端口 80
    targetPort: 80 # 转发到容器端口 80
  type: LoadBalancer #  Service 类型，Minikube 环境可以使用 LoadBalancer 或 NodePort