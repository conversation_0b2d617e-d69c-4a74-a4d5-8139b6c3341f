apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      initContainers:
      - name: init-chmod
        image: busybox:1.31.1
        command: ["sh", "-c", "chmod -R 777 /var/lib/grafana"]
        volumeMounts:
          - mountPath: "/var/lib/grafana"
            name: grafana-storage
      containers:
      - name: grafana
        image: grafana/grafana:11.0.0
        ports:
        - containerPort: 3000
        resources:
          limits:
            cpu: 500m
            memory: 1500Mi
          requests:
            cpu: 500m
            memory: 1500Mi
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
          # subPath 是指挂载的 PV（持久卷）中的二级目录（子目录），而不是 Pod 里的虚拟目录。
          # 例如，如果你的 PV 里有多个子目录，你可以通过 subPath 只把其中一个子目录挂载到容器的 mountPath 下。
          #subPath: pvc1-data
        - name: grafana-datasource
          mountPath: /etc/grafana/provisioning/datasources
          readOnly: true
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-pvc
      - name: grafana-datasource
        configMap:
          name: grafana-datasource

---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: monitoring
spec:
  selector:
    app: grafana
  ports:
    - port: 3000
      targetPort: 3000
      nodePort: 32000
  type: NodePort