apiVersion: v1
kind: PersistentVolume
metadata:
  name: grafana-pv  
  labels:
    pv-type: grafana
spec:
  capacity:
    storage: 1Gi
  accessModes:
    # 还有以下 accessModes 可选：
    # - ReadWriteOnce: 卷可以被单个节点（即某一台 Node 节点）以读写方式挂载（最常用）。
    #   注意：这里的“单个节点”指的是 Kubernetes 集群中的一台物理或虚拟机（Node），
    #   只要这个 PV 已经被某个节点上的 Pod 以读写方式挂载，其他节点上的 Pod 无法再以读写方式挂载同一个 PV。
    #   如果你有多个 Pod 分布在不同的节点，并且它们都尝试挂载同一个 ReadWriteOnce 的 PV，会导致只有一个 Pod 能成功挂载，
    #   其他 Pod 会一直处于 Pending 状态，直到 PV 被释放或调度到同一个节点。
    # - ReadOnlyMany: 卷可以被多个节点以只读方式挂载。
    # - ReadWriteMany: 卷可以被多个节点以读写方式挂载。
    # - ReadWriteOncePod: 卷只能被单个 Pod 以读写方式挂载（Kubernetes 1.22+）。
    - ReadWriteOnce
  #hostPath:
    #path: "/mnt/k8s-grafana-storage" # hostPath 仅适用于 minikube，本地路径可以根据实际情况修改
  local:
    path: /mnt/k8s-grafana-storage # 需要提前去 node 上创建 /mnt/k8s-grafana-storage 目录，否则 local PV 无法绑定
  nodeAffinity:  # 指定 PV 绑定到哪个 node 上
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          # minikube 的 hostname 通常是 "minikube"
          - minikube
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "" # 指定空存储类，避免使用默认存储类

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  selector:  # 添加 selector 字段
    matchLabels: #  使用 matchLabels 进行标签匹配
      pv-type: grafana #  指定要匹配的标签和值，这里要和 nas1 PV 上添加的标签一致
  storageClassName: "" # 指定空存储类，避免使用默认存储类