# 创建账号 ServiceAccount  
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cadvisor-sa
  namespace: monitoring
---
#创建权限 ClusterRole   
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cadvisor-role
rules:
  - apiGroups: [""]
    resources:
      - nodes/proxy
      - nodes/stats
      - nodes/metrics
      - nodes/spec
      - nodes
    verbs: ["get", "list", "watch"]
---
#创建角色和账号的绑定关系
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cadvisor-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cadvisor-role
subjects:
  - kind: ServiceAccount
    name: cadvisor-sa
    namespace: monitoring