apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: cadvisor
  namespace: monitoring
  labels:
    k8s-app: cadvisor
spec:
  selector:
    matchLabels:
      k8s-app: cadvisor
  template:
    metadata:
      labels:
        k8s-app: cadvisor
    spec:
      #hostNetwork: true
      serviceAccountName: cadvisor-sa  # 指定使用的 ServiceAccount
      containers:
      - name: cadvisor
        image: gcr.io/cadvisor/cadvisor:v0.39.3
        ports:
        - containerPort: 8080
          protocol: TCP
        resources:
          limits:
            cpu: 500m
            memory: 2500Mi
          requests:
            cpu: 500m
            memory: 2500Mi
        volumeMounts:
        - name: rootfs
          mountPath: /rootfs:ro
        - name: var-run
          mountPath: /var/run
          readOnly: false  # 调整为可写模式
        - name: sys
          mountPath: /sys:ro
        - name: docker
          mountPath: /var/lib/docker:ro
      volumes:
      - name: rootfs
        hostPath:
          path: /
      - name: var-run
        hostPath:
          path: /var/run
      - name: sys
        hostPath:
          path: /sys
      - name: docker
        hostPath:
          path: /var/lib/docker