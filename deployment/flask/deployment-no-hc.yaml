apiVersion: apps/v1
kind: Deployment
metadata:
  name: simple-flask-deployment
  namespace: app-namespace
spec:
  #hostNetwork: true # 使用host网络模式
  replicas: 2
  selector:
    matchLabels:
      app: simple-flask-app
  strategy:
    type: RollingUpdate #  确保滚动更新策略类型为 RollingUpdate
    rollingUpdate: #  配置滚动更新策略
      maxSurge: 1 #  滚动更新过程中，最多可以**额外**创建 2 个新的 Pod (超出 Desired 副本数)
      maxUnavailable: 1 #  滚动更新过程中，最多可以**同时**有多少个 Pod 处于不可用状态
  template:
    metadata:
      labels:
        app: simple-flask-app
        inject-sidecar: "true"
      annotations: 
        cloud.tencent.com/inject-sidecar: "true"  #接入APM
        cloud.tencent.com/otel-service-name: my-app #指定应用名
    spec:
      containers:
        - name: simple-flask-container
          image: simple-python-app-image:v2
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5000
          #  不再需要在 command 和 args 中指定，Dockerfile 已经定义了 CMD
          # command: ["python", "app.py"]
          # args: ["--host=0.0.0.0", "--port=5000"]
          env:
            - name: FLASK_APP
              value: app.py
            - name: FLASK_ENV
              value: development
            - name: FLASK_RUN_HOST
              value: 0.0.0.0  # 这些环境变量其实也可以在 Flask 代码中配置，或者通过 Flask 配置文件配置
            - name: FLASK_RUN_PORT  #  端口号通常在容器端口中声明，环境变量在这里可能不是必须的
              value: "5000"
          resources:  #  添加 resources 字段
            requests:  #  设置资源请求
              cpu: "1000m"   #  请求 0.2 个 CPU 核
              memory: "200Mi" #  请求 200 MiB 内存
            limits:    #  设置资源限制
              cpu: "1000m"   #  0.2 个 CPU 核
              memory: "200Mi" #  限制最多使用 200 MiB 内存
          startupProbe:
            httpGet:
              path: /healthz
              port: 5000
            failureThreshold: 30
            periodSeconds: 10
            initialDelaySeconds: 5
          readinessProbe:
            httpGet:
              path: /healthz
              port: 5000
            periodSeconds: 10
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /healthz
              port: 5000
            periodSeconds: 10
            failureThreshold: 3