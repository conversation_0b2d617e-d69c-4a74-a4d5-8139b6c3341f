apiVersion: v1
kind: Service
metadata:
  name: java-inject-sidecar-filler
  namespace: java-inject-ns
spec:
  type: LoadBalancer
  selector:
    app: java-inject-sidecar-filler
  ports:
    - name: http # Name the HTTP port
      protocol: TCP
      port: 80
      targetPort: 80
    - name: https # Name the HTTPS port
      protocol: TCP
      port: 443
      targetPort: 8443 # Target the container's 8443 for HTTPS
---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: java-inject-sidecar-filler
  namespace: java-inject-ns
  labels:
    inject-sidecar: "true"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: java-inject-sidecar-filler
  template:
    metadata:
      labels:
        app: java-inject-sidecar-filler
    spec:
      containers:
        - name: webhook
          image: my-mutating-admission:v11
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 80 # Expose HTTP port
            - containerPort: 8443 # Expose HTTPS port
          volumeMounts:
            - name: tls
              mountPath: "/app/tls"
              readOnly: true
          args: []
      volumes:
        - name: tls
          secret:
            secretName: java-inject-sidecar-tls

