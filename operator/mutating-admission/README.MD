docker build -t my-mutating-admission:v11 .
minikube image load my-mutating-admission:v11



kubectl apply -f service_deployment.yaml
sleep 10
kubectl get pods 
kubectl -n `kubectl get pods --all-namespaces -l app=java-inject-sidecar-filler --no-headers | awk '{print $1}'` logs `kubectl get pods --all-namespaces -l app=java-inject-sidecar-filler --no-headers | awk '{print $2}'`


kubectl port-forward svc/java-inject-sidecar-filler 80:80





pv

pvc  