apiVersion: v1
kind: Namespace
metadata:
  name: java-inject-ns
---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: selfsigned-issuer
  namespace: java-inject-ns
spec:
  selfSigned: {}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: java-inject-sidecar-cert   # 👈 改过的名称，避免引用旧的 Secret
  namespace: java-inject-ns
  annotations:
    cert-manager.io/set-ownerref: "true"  # 👈 添加这个注解
spec:
  secretName: java-inject-sidecar-tls  # 👈 保持原名，不用改 webhook 的配置
  duration: 8760h                      # 1年
  renewBefore: 720h                   # 可选，提前30天续签
  issuerRef:
    name: selfsigned-issuer
    kind: Issuer
    group: cert-manager.io
  commonName: java-inject-sidecar-filler.java-inject-ns.svc
  dnsNames:
    - java-inject-sidecar-filler
    - java-inject-sidecar-filler.java-inject-ns
    - java-inject-sidecar-filler.java-inject-ns.svc