from flask import Flask, request, jsonify
import base64
import json
import logging
import os

# --- 改进点 5: 通过环境变量配置日志路径 ---
# 使用环境变量 LOG_DIR，如果未设置，则默认为 /app/logs (适合容器环境)
# 在本地开发时，可以设置 export LOG_DIR=./logs
LOG_DIR = os.environ.get('LOG_DIR', '/app/logs')
# 如果是mac系统，就写到当前目录
if os.uname().sysname == 'Darwin':
    LOG_DIR = os.path.join(os.getcwd(), 'logs')

# 确保日志目录存在
if not os.path.exists(LOG_DIR):
    try:
        os.makedirs(LOG_DIR)
    except OSError as e:
        # 在并发环境下，可能另一个进程已经创建了目录，这不是一个错误
        if not os.path.isdir(LOG_DIR):
            raise

# 配置日志
# 在生产环境中，通常会使用更高级的日志配置，例如 JSON 格式化器，以便于日志聚合系统解析
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(LOG_DIR, 'app.log')),
        logging.StreamHandler()
    ]
)

# 为当前模块获取一个 logger 实例
logger = logging.getLogger(__name__)

app = Flask(__name__)


# --- 改进点 4: 将逻辑拆分为辅助函数 ---

def should_inject(pod: dict) -> bool:
    """
    根据 Pod 的 annotations 判断是否需要注入 sidecar。
    此函数包含幂等性检查：如果 sidecar 已存在，则返回 False。
    """
    # 1. 检查 Annotation
    annotations = pod.get('metadata', {}).get('annotations', {})
    inject_annotation = 'cloud.tencent.com/inject-sidecar'
    if annotations.get(inject_annotation, 'false').lower() != 'true':
        return False

    # 2. 幂等性检查：检查 sidecar 是否已存在
    sidecar_name = os.environ.get("SIDECAR_NAME", "noop-sidecar")
    containers = pod.get('spec', {}).get('containers', [])
    if any(c.get('name') == sidecar_name for c in containers):
        logger.info(f"Sidecar '{sidecar_name}' already exists in pod '{pod.get('metadata', {}).get('name')}'. Skipping injection.")
        return False

    return True


def create_sidecar_patch(pod: dict) -> list:
    """
    创建用于注入 sidecar 的 JSON Patch。
    """
    sidecar_name = os.environ.get("SIDECAR_NAME", "noop-sidecar")
    containers = pod.get('spec', {}).get('containers', [])

    # 从环境变量获取 sidecar 配置，提供合理的默认值
    sidecar_image = os.environ.get("SIDECAR_IMAGE", "busybox:1.36.1")
    sidecar_args_str = os.environ.get("SIDECAR_ARGS", '["/bin/sh", "-c", "while true; do sleep 3600; done"]')
    
    try:
        sidecar_args = json.loads(sidecar_args_str)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in SIDECAR_ARGS environment variable: {sidecar_args_str}")
        # 使用默认值作为回退
        sidecar_args = ["/bin/sh", "-c", "while true; do sleep 3600; done"]

    sidecar = {
        "name": sidecar_name,
        "image": sidecar_image,
        "args": sidecar_args,
        "resources": {},  # 建议在生产中定义明确的 resources (requests/limits)
        # --- 改进点 3: 增加安全上下文 ---
        "securityContext": {
            "runAsNonRoot": True,
            "runAsUser": 65534,  # 'nobody' user, a common choice for non-root containers
            "allowPrivilegeEscalation": False,
            "readOnlyRootFilesystem": True,
            "capabilities": {
                "drop": ["ALL"]
            }
        }
    }
    
    # 如果 spec.containers 不存在，则需要先创建它
    patch = []
    if not containers:
        patch.append({
            "op": "add",
            "path": "/spec/containers",
            "value": []
        })

    # 使用 '-' 在列表末尾添加元素，这是最安全的方式
    patch.append({
        "op": "add",
        "path": "/spec/containers/-",
        "value": sidecar
    })

    return patch


@app.route('/health', methods=['GET'])
def health():
    """健康检查端点"""
    return "ok", 200


@app.route('/mutate', methods=['POST'])
def mutate():
    """主变更逻辑端点"""
    try:
        admission_review = request.get_json()
        if not admission_review or 'request' not in admission_review:
            logger.error("Invalid AdmissionReview request received")
            return jsonify({"error": "Invalid request format"}), 400

        request_uid = admission_review['request']['uid']
        pod = admission_review['request']['object']

        logger.info(f"Received admission review request for UID: {request_uid}")

        if not should_inject(pod):
            # 日志记录已在 should_inject 内部处理
            return jsonify({
                "apiVersion": "admission.k8s.io/v1",
                "kind": "AdmissionReview",
                "response": {
                    "uid": request_uid,
                    "allowed": True
                }
            })

        patch = create_sidecar_patch(pod)

        patch_b64 = base64.b64encode(json.dumps(patch).encode()).decode()

        admission_response = {
            "apiVersion": "admission.k8s.io/v1",
            "kind": "AdmissionReview",
            "response": {
                "uid": request_uid,
                "allowed": True,
                "patchType": "JSONPatch",
                "patch": patch_b64
            }
        }
        return jsonify(admission_response)

    except Exception:
        # 使用 logger.exception 可以自动记录异常信息和堆栈跟踪
        logger.exception("Error processing admission review request")
        
        # 即使在异常情况下，也尝试获取 UID
        request_uid = None
        if request.is_json:
            request_data = request.get_json(silent=True)
            if request_data and 'request' in request_data:
                request_uid = request_data['request'].get('uid')

        return jsonify({
            "response": {
                "uid": request_uid,
                "allowed": False,
                "status": {
                    "message": "Webhook internal error. See webhook logs for details."
                }
            }
        }), 500


# --- 改进点 1: 移除线程模式，为生产级 WSGI 服务器做准备 ---
if __name__ == '__main__':
    # 这部分仅用于本地开发和调试。
    # 在生产环境中，你应该使用 Gunicorn 或 uWSGI 来运行此应用。
    
    # 例如，使用 Gunicorn 启动 HTTPS 服务:
    # gunicorn --workers 4 --bind 0.0.0.0:8443 --certfile /path/to/tls.crt --keyfile /path/to/tls.key 'main2:app'
    
    # 如果需要同时提供 HTTP (例如为了一个外部负载均衡器进行健康检查)，
    # 通常会配置 Gunicorn 监听一个端口，并让 Kubernetes 的 Service 或 Ingress 来处理路由。
    # 或者在 Pod 中运行一个反向代理（如 Nginx）来将请求转发到 Gunicorn。
    
    logger.info("Starting Flask development server on http://0.0.0.0:8080")
    # 使用一个与生产环境不同的端口以避免混淆
    app.run(host='0.0.0.0', port=8080, debug=True)
