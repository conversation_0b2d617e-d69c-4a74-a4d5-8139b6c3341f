# 使用官方 Python 3.10 镜像作为基础镜像
FROM python:3.10-slim-buster

# 设置工作目录
WORKDIR /app

# 将 requirements.txt 复制到工作目录
COPY requirements.txt .

# 安装 requirements.txt 中指定的 Python 依赖
RUN pip install -r requirements.txt

# 将 Flask 应用代码复制到工作目录
COPY main.py .

# 声明容器需要暴露的端口 (Flask 应用默认端口是 5000)
EXPOSE 5000
EXPOSE 80
EXPOSE 8443

# 设置容器启动时执行的命令 (启动 Flask 应用)
#CMD ["python", "main.py"]
CMD ["tail", "-f", "/dev/null"]
