from flask import Flask, request, jsonify
import base64
import json
import logging
import requests
import threading
import os

# 定义日志文件路径
LOG_DIR = '/app/logs'
# 如果是mac系统，就写到当前目录
if os.uname().sysname == 'Darwin':
    LOG_DIR = os.path.join(os.getcwd(), 'logs')
LOG_FILE_PATH = os.path.join(LOG_DIR, 'app.log')

# 确保日志目录存在
# 这一步非常重要，在配置 FileHandler 之前创建目录
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)
    logging.info(f"Created log directory: {LOG_DIR}") # 这条日志会先输出到stdout，因为它在basicConfig之前

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE_PATH, mode='a'), # 将日志写入文件，追加模式
        # 如果你希望日志也同时输出到控制台（stdout），可以再添加一个 StreamHandler
        logging.StreamHandler()
    ]
)

app = Flask(__name__)


@app.route('/', methods=['GET'])
def health():
    return "ok", 200


@app.route('/mutate', methods=['POST'])
def mutate():
    try:
        admission_review = request.get_json()
        # 详细记录收到的请求，便于调试
        logging.info(f"Received admission review request: {json.dumps(admission_review, indent=2)}")

        request_uid = admission_review['request']['uid']
        pod = admission_review['request']['object']
        annotations = pod.get('metadata', {}).get('annotations', {})

        inject_java = annotations.get('cloud.tencent.com/inject-sidecar', 'false').lower()
        if inject_java != 'true':
            # 如果不需要注入，则直接允许请求通过，不进行任何修改
            return jsonify({
                "apiVersion": "admission.k8s.io/v1",
                "kind": "AdmissionReview",
                "response": {
                    "uid": request_uid,
                    "allowed": True
                }
            })

        # 构造要添加的 sidecar 容器
        sidecar_name = os.environ.get("SIDECAR_NAME", "noop-sidecar")
        # 指定busybox版本号是 1.36.1
        sidecar_image = os.environ.get("SIDECAR_IMAGE", "busybox:1.36.1")
        sidecar_args_str = os.environ.get("SIDECAR_ARGS", '["/bin/sh", "-c", "while true; do sleep 3600; done"]')

        sidecar = {
            "name": sidecar_name,
            "image": sidecar_image,
            "args": json.loads(sidecar_args_str),
            "resources": {},
        }

        # Patch 操作，类型为 JSON Patch (RFC 6902)
        patch = [{
            "op": "add",
            "path": "/spec/containers/-",
            "value": sidecar
        }]

        # Base64 编码 patch 内容
        patch_b64 = base64.b64encode(json.dumps(patch).encode()).decode()

        admission_response = {
            "apiVersion": "admission.k8s.io/v1",
            "kind": "AdmissionReview",
            "response": {
                "uid": request_uid,
                "allowed": True,
                "patchType": "JSONPatch",
                "patch": patch_b64
            }
        }

        return jsonify(admission_response)

        
    except Exception as e:
        # 记录完整的异常堆栈信息
        logging.exception("Error processing admission review request")
        # 发生异常时，拒绝请求，这样Pod创建会失败，符合webhook配置中的 `failurePolicy: Fail`
        # 这样可以防止部署一个不完整的、或因webhook错误而未被正确修改的Pod
        # 尝试从请求中获取uid，以便API Server能够对应上是哪个请求失败了
        request_uid = request.get_json(silent=True).get('request', {}).get('uid') if request.is_json else None
        
        return jsonify({
            "apiVersion": "admission.k8s.io/v1",
            "kind": "AdmissionReview",
            "response": {
                "uid": request_uid,
                "allowed": False,
                "status": {
                    "message": f"Webhook internal error: {str(e)}"
                }
            }
        }), 500

def run_https():
    # 判断证书是否存在，如果不存在，那么打印error日志，但是不启动8443
    if not os.path.exists('/app/tls/tls.crt') or not os.path.exists('/app/tls/tls.key'):
        logging.error("Certificate files not found, skipping HTTPS server")
        return
    # 如果存在，那么启动8443端口
    logging.info("Starting HTTPS server on port 8443")
    app.run(host='0.0.0.0', port=8443, ssl_context=('/app/tls/tls.crt', '/app/tls/tls.key'))

def run_http():
    app.run(host='0.0.0.0', port=80)

if __name__ == '__main__':
    # 暴露2个端口
    t1 = threading.Thread(target=run_https)
    t2 = threading.Thread(target=run_http)
    t1.start()
    t2.start()
    t1.join()
    t2.join()
