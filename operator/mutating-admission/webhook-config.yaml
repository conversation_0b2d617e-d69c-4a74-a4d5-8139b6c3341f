apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: java-inject-sidecar
  annotations:
    # 这里使用 cert-manager.io/inject-ca-from: java-inject-ns/java-inject-sidecar-cert（Certificate 资源），
    # 而不是 cert-manager.io/inject-ca-from-secret: java-inject-ns/java-inject-sidecar-tls（Secret 资源），
    # 是因为 cert-manager 官方推荐直接引用 Certificate 对象，cert-manager 会自动追踪其生成的 Secret 并注入 CA。
    # 这样可以避免 Secret 被删除或重建时 CA 注入失效的问题，提升健壮性和自动化程度。
    # cert-manager.io/inject-ca-from-secret: java-inject-ns/java-inject-sidecar-tls
    cert-manager.io/inject-ca-from: java-inject-ns/java-inject-sidecar-cert
webhooks:
  - name: inject-java.cloud.tencent.com
    clientConfig:
      service:
        # 最终发出的地址是 Error from server (InternalError): error when creating "deployment-test-pod.yaml": Internal error occurred: failed calling webhook "inject-java.cloud.tencent.com": 
        # failed to call webhook: Post "https://java-inject-sidecar-filler.java-inject-ns.svc:443/mutate?timeout=10s": dial tcp 10.98.122.95:443: connect: connection refused
        name: java-inject-sidecar-filler
        namespace: java-inject-ns
        path: /mutate
        port: 443
      caBundle: null  # 👈 留空，cert-manager根据cert-manager.io/inject-ca-from: java-inject-ns/java-inject-sidecar-cert 自动填充
      # caBundle: ${caBundle} 手动填充的值来自于  kubectl get secret java-inject-sidecar-tls -n java-inject-ns -o jsonpath='{.data.ca\.crt}' | base64 -d
    rules:
      - apiGroups: [""]
        apiVersions: ["v1"]
        operations: ["CREATE"]
        resources: ["pods"]
    # 这表示只要 Pod 所在的 namespace 有这个 label，Webhook 就会拦截这个 Pod 的创建。所以你只打一次 label，整个 namespace 下的 Pod 创建都会生效 ✅。
    # 如果namespace 没有这个lable，但是应用有这个lable，也是不生效的
    #namespaceSelector:
    #  matchLabels:
    #    inject-sidecar: "true"
    # namespaceSelector 和 namespaceSelector 是& 关系，同时满足
    objectSelector:
      matchLabels:
        inject-sidecar: "true"
    admissionReviewVersions: ["v1"]
    sideEffects: None
    #如果 webhook 服务挂掉了，Pod 会创建失败！不会继续发布。  Fail的含义是：
      #✅ 如果 webhook 正常，执行变更或返回 200，K8s 接着走；
      #❌ 如果 webhook 超时、连接失败、证书错、挂掉了、响应 5xx，则 Pod 创建失败，会阻断发布流程。
    failurePolicy: Fail

