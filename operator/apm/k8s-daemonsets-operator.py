import kopf
import logging
import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 假设的告警接口地址
ALERT_URL = "http://example.com/api/alert"

def send_alert(app_label, message):
    
    logging.info(f"Alert sent for app: {app_label} with message: '{message}'")
    payload = {
        "app": app_label,
        "message": message
    }
    try:
        #response = requests.post(ALERT_URL, json=payload)
        #response.raise_for_status()
        logging.info(f"")
    except requests.exceptions.RequestException as e:
        logging.error(f"Failed to send alert for app: {app_label}. Error: {e}")

@kopf.on.create('apps', 'v1', 'daemonsets')
def on_daemonset_create(spec, meta, namespace, **kwargs):
    app_label = meta.get('labels', {}).get('k8s-app', 'unknown-app')
    daemonset_name = meta.get('name')
    logging.info(f"DaemonSet '{daemonset_name}' creation started in namespace '{namespace}'.")
    send_alert(app_label, f"DaemonSet '{daemonset_name}' creation started.")

@kopf.on.update('apps', 'v1', 'daemonsets')
def on_daemonset_update(spec, meta, status, namespace, **kwargs):
    app_label = meta.get('labels', {}).get('k8s-app', 'unknown-app')
    daemonset_name = meta.get('name')

    desired_number_scheduled = status.get('desiredNumberScheduled', 0)
    current_number_scheduled = status.get('currentNumberScheduled', 0)
    number_available = status.get('numberAvailable', 0)

    logging.info(f"desired_number_scheduled''': {desired_number_scheduled}")
    logging.info(f"current_number_scheduled': {current_number_scheduled}")
    logging.info(f"number_available': {number_available}")

    # 发布成功一台时通知
    if current_number_scheduled > number_available:
        logging.info(f"DaemonSet '{daemonset_name}' scheduled on one more node. Total available: {number_available}.")
        send_alert(app_label, f"DaemonSet '{daemonset_name}' scheduled successfully on a node. Total available: {number_available}.")

    # 发布失败时通知
    conditions = status.get('conditions', [])
    for condition in conditions:
        if condition.get('type') == 'Progressing' and condition.get('status') == 'False':
            reason = condition.get('reason', 'UnknownReason')
            message = condition.get('message', 'No detailed message available.')
            logging.error(f"Deployment of DaemonSet '{daemonset_name}' failed. Reason: {reason}, Message: {message}")
            send_alert(app_label, f"Deployment of DaemonSet '{daemonset_name}' failed. Reason: {reason}, Message: {message}")
            break

    # 发布结束通知
    if desired_number_scheduled == number_available:
        logging.info(f"Deployment of DaemonSet '{daemonset_name}' completed successfully.")
        send_alert(app_label, f"Deployment of DaemonSet '{daemonset_name}' completed successfully.")

if __name__ == '__main__':
    # 这段代码用于启动 Kopf 操作符框架，监听和处理 Kubernetes DaemonSet 相关的事件（如创建、更新等），
    # 以便在 DaemonSet 发生变更时自动执行自定义的 Python 处理逻辑（如发送告警、记录日志等）。
    kopf.run()