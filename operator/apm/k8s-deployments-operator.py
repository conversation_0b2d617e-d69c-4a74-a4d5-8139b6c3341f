import kopf
import kubernetes.client
from kubernetes.client.rest import ApiException
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()  # 可以根据需要添加 FileHandler 来记录到文件
    ]
)

@kopf.on.create('apps', 'v1', 'deployments')
def handle_create(spec, meta, namespace, **kwargs):
    annotations = spec.get('template', {}).get('metadata', {}).get('annotations', {})
    
    inject_java = annotations.get('cloud.tencent.com/inject-java', 'false') == 'true'
    service_name = annotations.get('cloud.tencent.com/otel-service-name')
    logging.info(f"Updating deployment {meta['name']} in namespace {namespace} with inject_java={inject_java} and service_name={service_name}")


    if inject_java and service_name:
        containers = spec.get('template', {}).get('spec', {}).get('containers', [])
        
        for container in containers:
            if 'java' in container.get('image', ''):  # 简单判断是否为Java应用
                # 添加 Java 参数
                java_opts = f"-Dapm.app={service_name}"
                
                # 更新环境变量 JAVA_TOOL_OPTIONS
                env_vars = container.setdefault('env', [])
                env_vars.append({'name': 'JAVA_TOOL_OPTIONS', 'value': java_opts})

                # 打印日志信息
                logging.info(f"Updating deployment {meta['name']} in namespace {namespace} with JAVA_TOOL_OPTIONS={java_opts}")

                # 更新 Deployment
                api = kubernetes.client.AppsV1Api()
                body = {
                    'spec': {
                        'template': {
                            'spec': {
                                'containers': containers
                            }
                        }
                    }
                }
                try:
                    api.patch_namespaced_deployment(name=meta['name'], namespace=namespace, body=body)
                    logging.info(f"Successfully updated deployment {meta['name']}")
                except ApiException as e:
                    logging.error(f"Failed to update deployment {meta['name']}: {e}")

if __name__ == '__main__':
    # 启动 Kopf 操作符
    kopf.run()