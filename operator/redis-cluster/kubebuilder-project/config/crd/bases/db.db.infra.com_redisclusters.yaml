---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: redisclusters.db.db.infra.com
spec:
  group: db.db.infra.com
  names:
    kind: RedisCluster
    listKind: RedisClusterList
    plural: redisclusters
    singular: rediscluster
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: RedisCluster is the Schema for the redisclusters API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: RedisClusterSpec defines the desired state of RedisCluster
            properties:
              backup:
                description: Backup spec for the Redis cluster.
                properties:
                  schedule:
                    description: Schedule for backups.
                    type: string
                  storageLocation:
                    description: StorageLocation for backups.
                    type: string
                type: object
              mode:
                default: cluster
                description: Mode of the Redis cluster (e.g., "cluster", "standalone",
                  "sentinel").
                enum:
                - cluster
                - standalone
                - sentinel
                type: string
              replicas:
                default: 3
                description: Replicas is the number of desired replicas.
                format: int32
                minimum: 1
                type: integer
              storage:
                description: Storage spec for the Redis cluster.
                properties:
                  size:
                    description: Size of the persistent volume claim.
                    type: string
                type: object
              version:
                description: Version of the Redis cluster.
                type: string
            required:
            - version
            type: object
          status:
            description: RedisClusterStatus defines the observed state of RedisCluster
            properties:
              lastBackupTime:
                description: LastBackupTime is the time of the last successful backup.
                format: date-time
                type: string
              master:
                description: Master is the name of the current master pod.
                type: string
              phase:
                description: Phase is the current state of the cluster.
                type: string
              readyReplicas:
                description: ReadyReplicas is the number of ready replicas.
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
