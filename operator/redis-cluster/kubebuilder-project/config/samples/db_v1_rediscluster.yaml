apiVersion: db.infra.com/v1
kind: RedisCluster
metadata:
  labels:
    app.kubernetes.io/name: kubebuilder-project
    app.kubernetes.io/managed-by: kustomize
  name: rediscluster-sample
spec:
  # Redis版本（必填）
  version: "7.0"
  
  # Redis模式：cluster, standalone, sentinel
  # 默认值: cluster
  mode: cluster
  
  # 副本数量
  # 默认值: 3
  replicas: 3
  
  # 存储配置
  storage:
    # 存储大小（创建后不可修改）
    # 默认值: 1Gi
    # 注意：StatefulSet创建后，存储大小不能修改
    size: "1Gi"
    # 不指定storageClassName，使用集群默认的StorageClass（动态PV）
  
  # 备份配置（可选，尚未实现）
  # backup:
  #   schedule: "0 2 * * *"
  #   storageLocation: "s3://backup-bucket/redis"
