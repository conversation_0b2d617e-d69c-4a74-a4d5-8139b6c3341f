# permissions for end users to edit redisclusters.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: kubebuilder-project
    app.kubernetes.io/managed-by: kustomize
  name: rediscluster-editor-role
rules:
- apiGroups:
  - db.db.infra.com
  resources:
  - redisclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - db.db.infra.com
  resources:
  - redisclusters/status
  verbs:
  - get
