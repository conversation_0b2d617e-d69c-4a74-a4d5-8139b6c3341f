# permissions for end users to view redisclusters.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: kubebuilder-project
    app.kubernetes.io/managed-by: kustomize
  name: rediscluster-viewer-role
rules:
- apiGroups:
  - db.db.infra.com
  resources:
  - redisclusters
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - db.db.infra.com
  resources:
  - redisclusters/status
  verbs:
  - get
