# Redis Operator Deployment Configuration
# 
# 使用说明：
# 1. 镜像构建: make docker-build IMG=your-registry/redis-operator:v1.0.0
# 2. 推送镜像: make docker-push IMG=your-registry/redis-operator:v1.0.0  
# 3. 部署前先安装 CRD: make install
# 4. 部署: kubectl apply -f redis-operator-deployment.yaml
#
# 关于启动方式的选择：
# - 生产环境：推荐使用编译后的二进制文件 (/manager)，性能好，资源占用少
# - 开发调试：可以考虑 go run，但需要在镜像中包含 Go 运行时和源码
---
apiVersion: v1
kind: Namespace
metadata:
  name: redis-operator-system
  labels:
    name: redis-operator-system
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: namespace

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: redis-operator-controller-manager
  namespace: redis-operator-system
  labels:
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: controller-manager

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: redis-operator-manager-role
  labels:
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: rbac
rules:
- apiGroups:
  - ""
  resources:
  - persistentvolumeclaims
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - pods/exec
  verbs:
  - create
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - batch
  resources:
  - jobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - db.infra.com
  resources:
  - redisclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - db.infra.com
  resources:
  - redisclusters/finalizers
  verbs:
  - update
- apiGroups:
  - db.infra.com
  resources:
  - redisclusters/status
  verbs:
  - get
  - patch
  - update

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: redis-operator-manager-rolebinding
  labels:
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: rbac
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: redis-operator-manager-role
subjects:
- kind: ServiceAccount
  name: redis-operator-controller-manager
  namespace: redis-operator-system

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: redis-operator-leader-election-role
  namespace: redis-operator-system
  labels:
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: rbac
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: redis-operator-leader-election-rolebinding
  namespace: redis-operator-system
  labels:
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: rbac
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: redis-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: redis-operator-controller-manager
  namespace: redis-operator-system

---
apiVersion: v1
kind: Service
metadata:
  name: redis-operator-controller-manager-metrics-service
  namespace: redis-operator-system
  labels:
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: controller-manager
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: https
  selector:
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: controller-manager

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-operator-controller-manager
  namespace: redis-operator-system
  labels:
    app.kubernetes.io/name: redis-operator
    app.kubernetes.io/component: controller-manager
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: redis-operator
      app.kubernetes.io/component: controller-manager
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        app.kubernetes.io/name: redis-operator
        app.kubernetes.io/component: controller-manager
    spec:
      # 安全上下文
      securityContext:
        runAsNonRoot: true
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
        # Kubernetes 1.19+ 支持
        seccompProfile:
          type: RuntimeDefault
      
      # 使用专用的 ServiceAccount
      serviceAccountName: redis-operator-controller-manager
      
      # 优雅终止
      terminationGracePeriodSeconds: 10
      
      # 节点选择（可选，用于多架构支持）
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
                - arm64
              - key: kubernetes.io/os
                operator: In
                values:
                - linux
      
      containers:
      - name: manager
        # 镜像配置 - 需要根据你的实际情况修改
        # 选项1: 使用本地构建的镜像
        image: redis-operator:latest
        # 选项2: 使用远程仓库镜像（推荐生产环境）
        # image: your-registry.com/redis-operator:v1.0.0
        # 选项3: 开发测试用的镜像
        # image: your-registry.com/redis-operator:dev
        
        imagePullPolicy: IfNotPresent  # 本地开发用 IfNotPresent，生产用 Always
        
        # 启动命令 - 使用编译后的二进制文件（推荐）
        command:
        - /manager
        
        # 启动参数
        args:
        - --leader-elect                          # 启用 leader election
        - --health-probe-bind-address=:8081       # 健康检查端口
        - --metrics-bind-address=127.0.0.1:8080  # Metrics 端口
        
        # 替代方案：使用 go run（仅用于开发调试）
        # 如果使用这种方式，需要确保镜像包含 Go 运行时和源码
        # command:
        # - go
        # args:
        # - run
        # - cmd/main.go
        # - --leader-elect
        # - --health-probe-bind-address=:8081
        
        # 端口配置
        ports:
        - containerPort: 8081
          name: healthz
          protocol: TCP
        - containerPort: 8080
          name: metrics
          protocol: TCP
        
        # 安全上下文
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        
        # 健康检查
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # 资源限制（根据实际需求调整）
        resources:
          limits:
            cpu: 500m
            memory: 256Mi      # 适当增加内存限制
          requests:
            cpu: 10m
            memory: 64Mi
        
        # 环境变量（可选）
        env:
        - name: OPERATOR_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: OPERATOR_NAME
          value: redis-operator-controller-manager
        
        # 卷挂载（如果需要）
        # volumeMounts:
        # - name: tmp
        #   mountPath: /tmp
      
      # 卷定义（如果需要）
      # volumes:
      # - name: tmp
      #   emptyDir: {}