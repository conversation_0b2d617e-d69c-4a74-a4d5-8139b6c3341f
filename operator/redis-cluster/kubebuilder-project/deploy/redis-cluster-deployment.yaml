apiVersion: "db.infra.com/v1"
kind: "RedisCluster"
metadata:
  name: "user-session-cache"
spec:
  version: "7.0"
  mode: "cluster" # 或者 "standalone", "sentinel"
  replicas: 3 # 1 master, 2 slaves
  storage:
    size: "10Gi"
  backup:
    schedule: "0 2 * * *" # 每天凌晨2点备份
    storageLocation: "s3://my-backup-bucket/redis/"
status:
  readyReplicas: 3
  master: "user-session-cache-0.redis.svc.cluster.local"
  lastBackupTime: "2025-07-17T02:00:10Z"
  phase: "Ready"