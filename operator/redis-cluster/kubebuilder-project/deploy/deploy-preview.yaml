apiVersion: v1
kind: Namespace
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: kubebuilder-project
    control-plane: controller-manager
  name: kubebuilder-project-system
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: redisclusters.db.db.infra.com
spec:
  group: db.db.infra.com
  names:
    kind: RedisCluster
    listKind: RedisClusterList
    plural: redisclusters
    singular: rediscluster
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: RedisCluster is the Schema for the redisclusters API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: RedisClusterSpec defines the desired state of RedisCluster
            properties:
              backup:
                description: Backup spec for the Redis cluster.
                properties:
                  schedule:
                    description: Schedule for backups.
                    type: string
                  storageLocation:
                    description: StorageLocation for backups.
                    type: string
                type: object
              mode:
                default: cluster
                description: Mode of the Redis cluster (e.g., "cluster", "standalone",
                  "sentinel").
                enum:
                - cluster
                - standalone
                - sentinel
                type: string
              replicas:
                default: 3
                description: Replicas is the number of desired replicas.
                format: int32
                minimum: 1
                type: integer
              storage:
                description: Storage spec for the Redis cluster.
                properties:
                  size:
                    description: Size of the persistent volume claim.
                    type: string
                type: object
              version:
                description: Version of the Redis cluster.
                type: string
            required:
            - version
            type: object
          status:
            description: RedisClusterStatus defines the observed state of RedisCluster
            properties:
              lastBackupTime:
                description: LastBackupTime is the time of the last successful backup.
                format: date-time
                type: string
              master:
                description: Master is the name of the current master pod.
                type: string
              phase:
                description: Phase is the current state of the cluster.
                type: string
              readyReplicas:
                description: ReadyReplicas is the number of ready replicas.
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: kubebuilder-project
  name: kubebuilder-project-controller-manager
  namespace: kubebuilder-project-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: kubebuilder-project
  name: kubebuilder-project-leader-election-role
  namespace: kubebuilder-project-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kubebuilder-project-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - persistentvolumeclaims
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - pods/exec
  verbs:
  - create
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - batch
  resources:
  - jobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - db.infra.com
  resources:
  - redisclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - db.infra.com
  resources:
  - redisclusters/finalizers
  verbs:
  - update
- apiGroups:
  - db.infra.com
  resources:
  - redisclusters/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: kubebuilder-project
  name: kubebuilder-project-rediscluster-editor-role
rules:
- apiGroups:
  - db.db.infra.com
  resources:
  - redisclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - db.db.infra.com
  resources:
  - redisclusters/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: kubebuilder-project
  name: kubebuilder-project-rediscluster-viewer-role
rules:
- apiGroups:
  - db.db.infra.com
  resources:
  - redisclusters
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - db.db.infra.com
  resources:
  - redisclusters/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: kubebuilder-project
  name: kubebuilder-project-leader-election-rolebinding
  namespace: kubebuilder-project-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kubebuilder-project-leader-election-role
subjects:
- kind: ServiceAccount
  name: kubebuilder-project-controller-manager
  namespace: kubebuilder-project-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: kubebuilder-project
  name: kubebuilder-project-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kubebuilder-project-manager-role
subjects:
- kind: ServiceAccount
  name: kubebuilder-project-controller-manager
  namespace: kubebuilder-project-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: kubebuilder-project
    control-plane: controller-manager
  name: kubebuilder-project-controller-manager
  namespace: kubebuilder-project-system
spec:
  replicas: 1
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
      containers:
      - args:
        - --leader-elect
        - --health-probe-bind-address=:8081
        command:
        - /manager
        image: redis-operator:latest
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        runAsNonRoot: true
      serviceAccountName: kubebuilder-project-controller-manager
      terminationGracePeriodSeconds: 10
