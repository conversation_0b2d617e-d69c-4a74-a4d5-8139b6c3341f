#!/bin/bash

# Redis Operator 部署预览脚本
# 用途：查看 make deploy 会创建哪些 Kubernetes 资源，而不实际部署
# 位置：deploy/ 目录下

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认镜像版本
IMG=${1:-redis-operator:latest}

echo -e "${GREEN}🔍 Redis Operator 部署预览${NC}"
echo -e "${BLUE}镜像版本: ${IMG}${NC}"
echo ""

# 进入项目根目录
cd "$(dirname "$0")/.."

# 确保工具存在
echo -e "${YELLOW}📋 检查必要工具...${NC}"
make manifests > /dev/null 2>&1

# 设置镜像版本
echo -e "${YELLOW}🔧 设置镜像版本...${NC}"
cd config/manager && ../../bin/kustomize* edit set image controller=${IMG} && cd ..

# 生成最终的部署 YAML
echo -e "${YELLOW}📦 生成部署资源预览...${NC}"
../bin/kustomize* build default > ../deploy/deploy-preview.yaml

# 回到 deploy 目录进行分析
cd ../deploy

# 分析并显示资源统计
echo -e "${GREEN}📊 部署资源统计:${NC}"
echo ""

# 统计各种资源
echo "📁 Namespace:"
grep -E "^kind: Namespace" deploy-preview.yaml | wc -l | sed 's/^/   /'
grep -A 2 "^kind: Namespace" deploy-preview.yaml | grep "name:" | awk '{print "   - " $2}'

echo ""
echo "🎯 CustomResourceDefinition (CRD):"
grep -E "^kind: CustomResourceDefinition" deploy-preview.yaml | wc -l | sed 's/^/   /'
grep -A 5 "^kind: CustomResourceDefinition" deploy-preview.yaml | grep "name:" | head -1 | awk '{print "   - " $2}'

echo ""
echo "👤 ServiceAccount:"
grep -E "^kind: ServiceAccount" deploy-preview.yaml | wc -l | sed 's/^/   /'
grep -A 5 "^kind: ServiceAccount" deploy-preview.yaml | grep "name:" | awk '{print "   - " $2}'

echo ""
echo "🔑 RBAC (Roles & Bindings):"
rbac_count=$(grep -E "^kind: (Role|ClusterRole|RoleBinding|ClusterRoleBinding)" deploy-preview.yaml | wc -l)
echo "   ${rbac_count}"
grep -E "^kind: (Role|ClusterRole)" deploy-preview.yaml | while read -r line; do
    kind=$(echo $line | awk '{print $2}')
    name=$(grep -A 3 "$line" deploy-preview.yaml | grep "name:" | head -1 | awk '{print $2}')
    echo "   - ${kind}: ${name}"
done

echo ""
echo "🚀 Deployment:"
grep -E "^kind: Deployment" deploy-preview.yaml | wc -l | sed 's/^/   /'
grep -A 10 "^kind: Deployment" deploy-preview.yaml | grep -E "(name:|replicas:|image:)" | while read -r line; do
    if echo "$line" | grep -q "name:"; then
        echo "   - $(echo $line | awk '{print $2}')"
    elif echo "$line" | grep -q "replicas:"; then
        echo "     副本数: $(echo $line | awk '{print $2}')"
    elif echo "$line" | grep -q "image:"; then
        echo "     镜像: $(echo $line | awk '{print $2}')"
    fi
done

echo ""
echo -e "${GREEN}📋 完整的 YAML 已保存到: deploy/deploy-preview.yaml${NC}"
echo -e "${BLUE}💡 查看完整内容: cat deploy/deploy-preview.yaml${NC}"
echo -e "${BLUE}🚀 实际部署命令: make deploy IMG=${IMG}${NC}"
echo ""
echo -e "${YELLOW}🔍 关键配置信息:${NC}"
echo "   • 命名空间: kubebuilder-project-system"
echo "   • ServiceAccount: kubebuilder-project-controller-manager"
echo "   • 权限: 包含 pods/exec 权限（支持 Redis 命令执行）"
echo "   • 部署名称: kubebuilder-project-controller-manager"
echo "   • 健康检查端口: 8081"
echo ""
echo -e "${BLUE}📂 使用方法:${NC}"
echo "   cd deploy && ./deploy-preview.sh [镜像版本]"
echo "   示例: ./deploy-preview.sh redis-operator:v1.0.0"