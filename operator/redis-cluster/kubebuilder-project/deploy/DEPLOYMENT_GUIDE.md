# Redis Operator 部署指南

## 🚀 快速部署

### 方案一：使用 Makefile（推荐）
```bash
# 1. 构建镜像
make docker-build IMG=redis-operator:v1.0.0

# 2. 安装 CRD
make install

# 3. 部署 operator
make deploy IMG=redis-operator:v1.0.0
```

### 方案二：使用自定义 Deployment YAML
```bash
# 1. 构建镜像
make docker-build IMG=redis-operator:latest

# 2. 安装 CRD（必须先安装）
kubectl apply -f config/crd/bases/

# 3. 部署 operator
kubectl apply -f redis-operator-deployment.yaml
```

## 📋 配置选项详解

### 1. 镜像选择
```yaml
# 选项1: 本地开发镜像
image: redis-operator:latest
imagePullPolicy: IfNotPresent

# 选项2: 生产环境镜像
image: your-registry.com/redis-operator:v1.0.0
imagePullPolicy: Always

# 选项3: 私有仓库镜像
image: harbor.company.com/infra/redis-operator:v1.0.0
imagePullPolicy: Always
```

### 2. 启动命令对比

#### 🎯 方案A：编译二进制（推荐生产环境）
**优点：**
- 启动快速（~1-2秒）
- 资源占用少（内存 ~64Mi）
- 镜像体积小（~20MB）
- 生产级稳定性

**缺点：**
- 需要编译步骤
- 调试相对困难

```yaml
command:
- /manager
args:
- --leader-elect
- --health-probe-bind-address=:8081
```

#### 🔧 方案B：go run（适合开发调试）
**优点：**
- 开发调试方便
- 可以实时修改代码
- 便于排查问题

**缺点：**
- 启动慢（~10-15秒）
- 资源占用大（内存 ~200Mi+）
- 镜像体积大（~800MB+）
- 需要 Go 运行时

```yaml
# 需要使用包含 Go 的基础镜像
image: golang:1.21
command:
- go
args:
- run
- cmd/main.go
- --leader-elect
```

### 3. Namespace 选择建议

#### 🏢 推荐方案：`redis-operator-system`
- 语义明确，便于识别
- 与其他 operator 隔离
- 符合 Kubernetes 命名约定

#### 🔄 替代方案：
```bash
# 基础设施类
kubectl create namespace infrastructure-system

# 数据库类
kubectl create namespace database-operators

# 通用系统类（Kubebuilder 默认）
kubectl create namespace system
```

## 🛠️ 构建和推送镜像

### 本地构建
```bash
# 构建镜像
make docker-build IMG=redis-operator:latest

# 查看镜像
docker images | grep redis-operator
```

### 推送到远程仓库
```bash
# 推送到 Docker Hub
make docker-build IMG=yourusername/redis-operator:v1.0.0
make docker-push IMG=yourusername/redis-operator:v1.0.0

# 推送到私有仓库
make docker-build IMG=your-registry.com/redis-operator:v1.0.0
make docker-push IMG=your-registry.com/redis-operator:v1.0.0
```

### 多架构构建
```bash
# 构建支持多架构的镜像
make docker-buildx IMG=yourusername/redis-operator:v1.0.0
```

## 🔍 验证部署

### 检查 Pod 状态
```bash
# 查看 operator pod
kubectl get pods -n redis-operator-system

# 查看日志
kubectl logs -n redis-operator-system deployment/redis-operator-controller-manager
```

### 检查 CRD
```bash
# 查看 CRD 是否安装成功
kubectl get crd | grep rediscluster

# 查看 CRD 详情
kubectl describe crd redisclusters.db.infra.com
```

### 检查 RBAC
```bash
# 检查 ServiceAccount
kubectl get sa -n redis-operator-system

# 检查权限
kubectl auth can-i create pods/exec --as=system:serviceaccount:redis-operator-system:redis-operator-controller-manager
```

## 🧪 测试部署

### 创建测试 RedisCluster
```yaml
apiVersion: db.infra.com/v1
kind: RedisCluster
metadata:
  name: test-redis
  namespace: default
spec:
  replicas: 3
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
```

### 应用测试
```bash
# 创建测试 CR
kubectl apply -f test-rediscluster.yaml

# 查看状态
kubectl get redisclusters
kubectl describe rediscluster test-redis
```

## 🔧 故障排查

### 常见问题

#### 1. 镜像拉取失败
```bash
# 检查镜像是否存在
docker pull redis-operator:latest

# 检查 ImagePullPolicy
kubectl describe pod -n redis-operator-system
```

#### 2. 权限问题
```bash
# 检查 RBAC 配置
kubectl get clusterrole redis-operator-manager-role -o yaml
kubectl get clusterrolebinding redis-operator-manager-rolebinding -o yaml
```

#### 3. CRD 未安装
```bash
# 安装 CRD
make install

# 或手动安装
kubectl apply -f config/crd/bases/
```

#### 4. 健康检查失败
```bash
# 检查健康端点
kubectl port-forward -n redis-operator-system svc/redis-operator-controller-manager-metrics-service 8081:8081
curl http://localhost:8081/healthz
```

## 🗑️ 清理资源

```bash
# 删除 operator
kubectl delete -f redis-operator-deployment.yaml

# 删除 CRD（会删除所有 RedisCluster 实例）
make uninstall

# 或使用 make 命令清理
make undeploy
```

## 🎯 最佳实践建议

1. **生产环境使用编译二进制 + 固定版本标签**
2. **开发环境可以使用 go run + latest 标签**
3. **使用专用 namespace 进行隔离**
4. **设置合适的资源限制**
5. **启用 leader election 防止多实例冲突**
6. **配置健康检查确保高可用**