# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Redis Cluster Kubernetes Operator built with Kubebuilder v4. The operator manages Redis cluster instances in Kubernetes, providing lifecycle management including creation, updates, scaling, and deletion with persistent storage support.

**Key Components:**
- **Custom Resource**: `RedisCluster` (group: db.infra.com/v1) 
- **Controller**: Manages StatefulSets, Services, and PersistentVolumeClaims
- **Modes**: Supports cluster, standalone, and sentinel Redis configurations
- **Storage**: Persistent volume support with configurable sizes
- **Backup**: Configurable backup scheduling (spec defined, implementation pending)

## Development Commands

### Building and Testing
```bash
# Build the manager binary
make build

# Run tests (unit tests, excluding e2e)
make test

# Run e2e tests against Kind cluster
make test-e2e

# Run linter
make lint

# Auto-fix linting issues
make lint-fix

# Format code
make fmt

# Vet code
make vet
```

### Code Generation
```bash
# Generate CRDs, RBAC, and webhook configurations
make manifests

# Generate DeepCopy methods
make generate
```

### Development Workflow
```bash
# Run controller locally (requires valid kubeconfig)
make run

# Install CRDs into cluster
make install

# Deploy controller to cluster
make deploy IMG=<registry>/kubebuilder-project:tag

# Apply sample resources
kubectl apply -k config/samples/

# Clean up
make undeploy
make uninstall
```

### Docker Operations
```bash
# Build and push image
make docker-build docker-push IMG=<registry>/kubebuilder-project:tag

# Cross-platform build
make docker-buildx IMG=<registry>/kubebuilder-project:tag

# Generate installer bundle
make build-installer IMG=<registry>/kubebuilder-project:tag
```

## Architecture

### Controller Logic (internal/controller/rediscluster_controller.go)
The controller implements a comprehensive reconciliation loop with:

**Lifecycle Management:**
- Finalizer handling for cleanup on deletion
- Phase tracking: Creating, Running, Updating, Deleting, Failed
- Configuration validation (version, replicas, mode)
- Status updates with ready replica counts

**Resource Management:**
- **StatefulSet**: Manages Redis pods with persistent storage
- **Services**: Headless service for StatefulSet + regular service for external access
- **PVCs**: Automatic persistent volume provisioning via volumeClaimTemplates
- **Pods**: Health monitoring and ready state tracking

**Key Methods:**
- `Reconcile()`: Main reconciliation loop
- `handleDeletion()`: Cleanup logic with finalizers
- `reconcileStatefulSet()`: StatefulSet management with update detection
- `reconcileService()`/`reconcileHeadlessService()`: Service management
- `updateRedisClusterStatus()`: Status synchronization
- `validateRedisCluster()`: Configuration validation

### API Types (api/v1/rediscluster_types.go)
**RedisClusterSpec:**
- `version`: Redis version (required)
- `mode`: cluster/standalone/sentinel (default: cluster) 
- `replicas`: Number of replicas (minimum 1, default 3)
- `storage.size`: PVC size (default: 1Gi)
- `backup`: Schedule and storage location (future feature)

**RedisClusterStatus:**
- `readyReplicas`: Count of ready pods
- `master`: Current master pod name
- `phase`: Current lifecycle phase
- `lastBackupTime`: Backup timestamp (future feature)

### Configuration Files
- **config/crd/bases/**: Generated CRD definitions
- **config/rbac/**: RBAC roles and bindings
- **config/manager/**: Deployment configuration
- **config/samples/**: Example RedisCluster resources

## Testing

**Unit Tests:** Located in `internal/controller/rediscluster_controller_test.go`
**E2E Tests:** Located in `test/e2e/` using Ginkgo/Gomega framework

Run specific test patterns:
```bash
# Run single test
go test ./internal/controller -run TestReconcile

# Run with coverage
make test
```

## Key Implementation Details

**Finalizer Pattern:** Uses `rediscluster.db.infra.com/finalizer` for proper cleanup
**Status Management:** Real-time status updates with phase transitions
**Storage Integration:** PVC templates in StatefulSet for data persistence
**Service Discovery:** Both headless and regular services for different access patterns
**Error Handling:** Retry mechanisms with exponential backoff (2min/5min intervals)
**Pod Monitoring:** Ready state checking using PodReady condition

## Dependencies

- **Go**: 1.21+
- **Kubernetes**: 1.11.3+
- **Controller-runtime**: v0.17.3
- **Kubebuilder**: v4 (scaffolding framework)
- **Testing**: Ginkgo v2.14.0, Gomega v1.30.0

## Configuration Notes

- Domain: `db.infra.com`
- Repository: `github.com/gougouzcm/redis-operator` 
- Default image: `controller:latest`
- Default storage: `1Gi`
- Default replicas: `3`
- Redis port: `6379`