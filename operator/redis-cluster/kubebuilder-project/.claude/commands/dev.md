# 开发环境部署

快速部署 Redis Operator 到开发环境进行测试。

## 执行步骤：

1. 安装 CRDs 到集群
2. 本地运行 controller
3. 应用示例 RedisCluster 资源

## 命令序列：

```bash
# 确保有 kubeconfig
if ! kubectl cluster-info &>/dev/null; then
    echo "❌ 无法连接到 Kubernetes 集群，请检查 kubeconfig"
    exit 1
fi

echo "📦 安装 CRDs..."
make install

echo "🚀 启动本地 controller..."
echo "提示：使用 Ctrl+C 停止 controller"
echo "在另一个终端运行以下命令应用示例："
echo "kubectl apply -f config/samples/db_v1_rediscluster.yaml"

# 运行 controller
make run
```