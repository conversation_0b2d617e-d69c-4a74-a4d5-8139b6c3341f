# access k8s app in chrome

访问easyops的api，获取应用IP信息：$ARGUMENTS。打开浏览器访问特定地址
  如果没有提供IP参数，提示用户输入IP地址。
  
## 按照以下步骤操作：

1. 掉用GET请求，http://leopard.111.com.cn/api/ledao/hosts_to_appname?ip={}
2. 获取接口的返回值，如果是空的，或者返回body里面出现了没有应用的字样，访问第二个接口 
   url2 = 'https://opsapi-stg.yiyaowang.com/k8s/select_app_ip_association_with_ip?ip={}'.format(ip)
  
    # Headers 和 Data
    headers = {
        'accept': 'application/json',
        'token': 'aCEQynlxDNnQ8qhY'
    }
    data = ''
    
    # 发送 POST 请求
    response2 = requests.post(url2, headers=headers, data=data)
    response_data = response2.json()
    
    # 获取 "data" 数组中的第一个元素
    if response_data["data"]:
        APPNAME = response_data["data"][0]
    else:
        APPNAME = u"找不到应用"


3. 获取应用名称后，打开浏览器 访问地址 
   https://console.cloud.tencent.com/monitor/apm/system/application?tab=performance&from=now-15m&to=now&service={APPNAME}&team=apm-ZM0NAhqX4&rid=4  应用名称用上一个步骤获取到APPNAME

## 其他说明
你可以简单叙述下过程，最终目的是打开chrome浏览器访问对应的地址
我是mac系统
