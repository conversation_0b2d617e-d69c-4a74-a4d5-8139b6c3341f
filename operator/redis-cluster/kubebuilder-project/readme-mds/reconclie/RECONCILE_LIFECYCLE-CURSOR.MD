# RedisCluster Reconcile 生命周期与触发机制详解

## 一、问题速览

- 针对 `kubectl apply -f redis-cluster-deployment.yaml`，`Reconcile` 会被触发多少次？
- 触发 Reconcile 的代码逻辑有哪些？
- k8s、kubebuilder、Reconcile 分别做了什么？
- 线性流程梳理
- 多副本 Pod 状态变化是否会多次触发？
- 扩容/Pod 异常场景下的 Reconcile 行为

---

## 二、阶段一：首次 apply 的 Reconcile 触发流程

### 1. 第一次 apply 会触发多少次 Reconcile？

- **最少 2 次，实际通常 5-10 次甚至更多。**
- 主要原因：
  - 第一次创建资源，添加 Finalizer，触发 2 次
  - 创建子资源（Service、StatefulSet），每个子资源的事件也会触发
  - Pod 状态变化（Pending→Running→Ready），每次变化都可能触发
  - Status 字段变化也可能触发

### 2. 代码哪些逻辑会触发 Reconcile？

- 主资源（RedisCluster）被创建、更新（如添加 Finalizer、Spec 变更）
- 子资源（StatefulSet、Service、PVC）被创建、更新、删除
- Pod 状态变化（如 Ready/NotReady）
- Status 字段变化（如 ReadyReplicas 变化）
- RequeueAfter 定时重试

### 3. 线性流程梳理（k8s/kubebuilder/自定义 Reconcile 各自做了什么）

#### 步骤 1：kubectl apply
- 用户执行 `kubectl apply -f redis-cluster-deployment.yaml`
- k8s API Server 创建 RedisCluster 资源，写入 etcd

#### 步骤 2：kubebuilder 控制器监听到事件
- controller-runtime 监听到 RedisCluster 新增事件，调度 Reconcile

#### 步骤 3：第一次 Reconcile
- 读取资源，发现无 Finalizer，添加 Finalizer，调用 `r.Update`，返回
- Finalizer 的添加会立即触发第二次 Reconcile

#### 步骤 4：第二次 Reconcile
- 读取资源，发现已有 Finalizer，进入主业务逻辑
- 校验 Spec 合法性
- 创建 Headless Service、普通 Service、StatefulSet
- 每创建一个子资源，k8s 也会发出事件，可能导致后续 Reconcile
- 更新 Status 字段（如 Phase=Creating）

#### 步骤 5：子资源和 Pod 状态变化
- StatefulSet 控制器启动 Pod，Pod 状态变化（Pending→Running→Ready）
- 每个 Pod 状态变化，都会被 controller-runtime watch 到，调度 Reconcile
- Reconcile 检查 Pod 状态，更新 RedisCluster.Status.ReadyReplicas、Phase、Master 等
- Status 字段变化也会再次触发 Reconcile

#### 备注
- 只要主资源或子资源有变化，都会触发 Reconcile
- 只要有 RequeueAfter（如出错），也会定时触发

### 4. 多副本 Pod 状态变化是否会多次触发？

- **是的。**
- 每个 Pod 的状态变化（如 Pending→Running→Ready）都会触发 Reconcile
- 例如 3 个副本，每个 Pod 变为 Ready 都会单独触发一次
- 只要 ReadyReplicas 变化，Status 更新，也会再次触发

---

## 三、阶段二：集群已创建，扩容副本

- 用户修改 YAML 或通过 kubectl patch，将 replicas 从 3 改为 4
- k8s API Server 更新 RedisCluster.Spec，发出事件，调度 Reconcile
- Reconcile 检查到副本数变化，更新 StatefulSet 副本数
- StatefulSet 控制器启动新 Pod，新 Pod 状态变化（Pending→Running→Ready）
- 每个 Pod 状态变化也会触发 Reconcile，直到 ReadyReplicas 达到期望值
- Status 字段变化也会触发 Reconcile
- **结论：扩容会触发多次 Reconcile，直到集群稳定**

---

## 四、阶段三：集群已创建，Pod 异常

- 某个 Pod 异常（如 CrashLoopBackOff、NotReady）
- Pod 状态变化，controller-runtime watch 到，调度 Reconcile
- Reconcile 检查 Pod 状态，发现 ReadyReplicas 下降，更新 Status（Phase=Updating/Failed）
- Status 字段变化也会触发 Reconcile
- Pod 恢复后，状态变化再次触发 Reconcile，直到集群恢复 Running
- **结论：Pod 异常/恢复都会多次触发 Reconcile**

---

## 五、结论与建议

1. **Reconcile 是事件驱动的，任何主资源/子资源/状态变化都会触发**
2. **首次 apply 至少触发 2 次 Reconcile，实际会更多**
3. **扩容、Pod 异常等场景下，Reconcile 也会多次触发，直到集群状态稳定**
4. **只要有 RequeueAfter 或子资源状态波动，Reconcile 会持续被调度**
5. **设计 Reconcile 时要保证幂等性和可重入性，避免副作用**

---

> 本文档由 AI 自动生成，供 RedisCluster Operator 开发与运维参考。 