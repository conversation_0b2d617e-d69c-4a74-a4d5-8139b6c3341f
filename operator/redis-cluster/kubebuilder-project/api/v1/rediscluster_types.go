/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// RedisClusterSpec defines the desired state of RedisCluster
type RedisClusterSpec struct {
	// Version of the Redis cluster.
	// +kubebuilder:validation:Required
	Version string `json:"version"`

	// Mode of the Redis cluster (e.g., "cluster", "standalone", "sentinel").
	// +kubebuilder:validation:Enum=cluster;standalone;sentinel
	// +kubebuilder:default=cluster
	Mode string `json:"mode,omitempty"`

	// Replicas is the number of desired replicas.
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:default=3
	Replicas *int32 `json:"replicas,omitempty"`

	// Storage spec for the Redis cluster.
	Storage StorageSpec `json:"storage,omitempty"`

	// Backup spec for the Redis cluster.
	Backup BackupSpec `json:"backup,omitempty"`
}

// StorageSpec defines the storage configuration for a Redis pod.
type StorageSpec struct {
	// Size of the persistent volume claim.
	Size string `json:"size,omitempty"`
}

// BackupSpec defines the backup configuration for the Redis cluster.
type BackupSpec struct {
	// Schedule for backups.
	Schedule string `json:"schedule,omitempty"`
	// StorageLocation for backups.
	StorageLocation string `json:"storageLocation,omitempty"`
}

// RedisClusterStatus defines the observed state of RedisCluster
type RedisClusterStatus struct {
	// ReadyReplicas is the number of ready replicas.
	ReadyReplicas int32 `json:"readyReplicas,omitempty"`

	// Master is the name of the current master pod.
	Master string `json:"master,omitempty"`

	// LastBackupTime is the time of the last successful backup.
	LastBackupTime *metav1.Time `json:"lastBackupTime,omitempty"`

	// Phase is the current state of the cluster.
	Phase string `json:"phase,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status

// RedisCluster is the Schema for the redisclusters API
type RedisCluster struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   RedisClusterSpec   `json:"spec,omitempty"`
	Status RedisClusterStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// RedisClusterList contains a list of RedisCluster
type RedisClusterList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []RedisCluster `json:"items"`
}

func init() {
	SchemeBuilder.Register(&RedisCluster{}, &RedisClusterList{})
}
