/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"bytes"
	"context"
	"fmt"
	"reflect"
	"strings"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/intstr"

	// 这些依赖来自 k8s.io/client-go 模块，在 go.mod 里通过如下方式声明依赖：
	// require (
	//     k8s.io/client-go v0.x.x
	// )
	// 实际安装由 go mod tidy/go mod download 自动完成
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"

	dbv1 "github.com/gougouzcm/redis-operator/api/v1"
)

const (
	// RedisClusterFinalizer is the finalizer for RedisCluster
	// RedisClusterFinalizer 是一个常量（constant），用于标识 RedisCluster 资源的 finalizer 字符串，不是方法也不是属性。
	RedisClusterFinalizer = "rediscluster.db.infra.com/finalizer"

	// Phase constants
	PhaseCreating = "Creating"
	PhaseRunning  = "Running"
	PhaseUpdating = "Updating"
	PhaseDeleting = "Deleting"
	PhaseFailed   = "Failed"

	// Default values
	DefaultRedisPort   = 6379
	DefaultStorageSize = "1Gi"

	// ConfigMap keys
	ReadinessScriptKey = "readiness.sh"

	// Annotation keys
	ClusterInitializedAnnotation = "rediscluster.db.infra.com/initialized"
)

// RedisClusterReconciler reconciles a RedisCluster object
type RedisClusterReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

//+kubebuilder:rbac:groups=db.infra.com,resources=redisclusters,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=db.infra.com,resources=redisclusters/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=db.infra.com,resources=redisclusters/finalizers,verbs=update
//+kubebuilder:rbac:groups=apps,resources=statefulsets,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups="",resources=services,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch
//+kubebuilder:rbac:groups="",resources=pods/exec,verbs=create
//+kubebuilder:rbac:groups="",resources=persistentvolumeclaims,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=batch,resources=jobs,verbs=get;list;watch;create;update;patch;delete

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.

// 当你执行 kubectl apply 时：
//
//	kubectl apply -f redis-cluster-deployment.yaml
//
//	# Kubernetes 会将你的 YAML 数据存储到 etcd 中
//	# 存储的数据就是：
//	{
//	  "apiVersion": "db.infra.com/v1",
//	  "kind": "RedisCluster",
//	  "metadata": {
//	    "name": "user-session-cache",
//	    "namespace": "default"
//	  },
//	  "spec": {
//	    "version": "7.0",
//	    "mode": "cluster",
//	    "replicas": 3,
//	    "storage": {
//	      "size": "10Gi"
//	    }
//	  }
//	}
//
//	# 然后 controller 会监听到这个事件，并触发 Reconcile 方法
//	# 在 Reconcile 方法中，会执行 r.Get(ctx, req.NamespacedName, redisCluster)
//	# 这相当于执行 kubectl -n default get rediscluster user-session-cache
//	# 如果 RedisCluster 资源不存在，会返回 "Error from server (NotFound): redisclusters.db.infra.com "user-session-cache" not found" 错误
//	# 如果 RedisCluster 资源存在，会返回成功
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := log.FromContext(ctx)

	log.V(1).Info("Reconciling RedisCluster", "NamespacedName", req.NamespacedName)

	// Fetch the RedisCluster instance
	// req.NamespacedName 包含两个信息: Namespace 和 Name (例如: {Namespace: "default", Name: "user-session-cache"})
	// r.Get() 会向 Kubernetes API Server 发送 GET 请求获取对应的资源
	// 实际执行: GET /apis/db.infra.com/v1/namespaces/{namespace}/redisclusters/{name}
	// 成功后会将从 etcd 中获取的资源数据反序列化并填充到 redisCluster 对象中
	redisCluster := &dbv1.RedisCluster{}                // 创建空的 RedisCluster 对象
	err := r.Get(ctx, req.NamespacedName, redisCluster) // 从 API Server 获取数据并填充到对象中
	if err != nil {
		if errors.IsNotFound(err) {
			// 资源不存在，通常是因为资源已被删除，这是正常情况
			log.Info("RedisCluster resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		// 其他错误（比如API资源未注册、权限问题、网络问题等）需要返回错误，让控制器重试
		log.Error(err, "Failed to get RedisCluster from API Server")
		return ctrl.Result{}, err
	}

	// 执行到这里说明成功获取到 RedisCluster 资源
	// redisCluster 对象现在包含了 YAML 文件中定义的所有数据:
	// - redisCluster.Name: "user-session-cache"
	// - redisCluster.Spec.Version: "7.0"
	// - redisCluster.Spec.Mode: "cluster"
	// - redisCluster.Spec.Replicas: 3
	// - redisCluster.Spec.Storage.Size: "10Gi"

	// ==================== 配置验证 ====================
	// 验证用户在 YAML 文件中提供的配置是否合法
	// 比如检查 Redis 版本是否为空、副本数是否合理等
	if err := r.validateRedisCluster(redisCluster); err != nil {
		log.Error(err, "Invalid RedisCluster configuration")
		r.updateStatus(ctx, redisCluster, PhaseFailed, 0, "")
		return ctrl.Result{RequeueAfter: time.Minute * 5}, err
	}

	// ==================== Finalizer 删除处理逻辑 ====================
	// 当用户执行 kubectl delete -f deployment.yaml 时的完整流程：
	//
	// 步骤1: 用户执行删除命令
	//   kubectl delete -f redis-cluster-deployment.yaml
	//   或者
	//   kubectl delete rediscluster user-session-cache
	//
	// 步骤2: kubectl 向 API Server 发送 DELETE 请求
	//   DELETE /apis/db.infra.com/v1/namespaces/default/redisclusters/user-session-cache
	//
	// 步骤3: API Server 检查资源是否有 Finalizer
	//   如果有 Finalizer，API Server 不会立即删除资源
	//   而是设置 metadata.deletionTimestamp 字段为当前时间
	//   资源状态变为 "Terminating"
	// 注意：由于我们在资源创建时就添加了 Finalizer（在 Reconcile 方法中），所以第一次删除时一定会有 Finalizer，会进入删除流程而不是立即删除
	//
	// 步骤4: Controller 监听到资源变化，触发 Reconcile
	//   我们的代码检查到 DeletionTimestamp != nil
	//   知道用户想要删除这个 RedisCluster
	//
	// 步骤5: 执行清理逻辑（在 handleDeletion 方法中）
	//   - 删除我们创建的 StatefulSet
	//   - 删除我们创建的 Service
	//   - 删除我们创建的 PVC（如果需要）
	//   - 其他清理工作
	//
	// 步骤6: 清理完成后，移除 Finalizer
	//   controllerutil.RemoveFinalizer(redisCluster, RedisClusterFinalizer)
	//
	// 步骤7: API Server 检测到 Finalizer 列表为空，真正删除 RedisCluster
	//   资源从 etcd 中彻底删除
	//
	// 重要说明：
	// - 我们控制的是 RedisCluster 这个自定义资源的删除流程
	// - 我们创建的 StatefulSet、Service 等子资源，是我们决定要不要删除
	// - 如果没有 Finalizer，用户删除 RedisCluster 时，子资源可能变成"孤儿资源"
	if redisCluster.DeletionTimestamp != nil {
		// DeletionTimestamp 不为空表示用户已经执行了删除操作
		// 现在需要执行清理逻辑
		return r.handleDeletion(ctx, redisCluster)
	}

	// ==================== Finalizer 添加逻辑 ====================
	// Finalizer 是一个字符串列表，存储在 metadata.finalizers 字段中
	// 它告诉 Kubernetes："在删除这个资源之前，必须先执行某些清理操作"
	//
	// 我们的 Finalizer 名称是: "rediscluster.db.infra.com/finalizer"
	// 这个名称是我们自己定义的，通常使用域名格式避免冲突
	//
	// 如果 RedisCluster 上没有这个 Finalizer，我们需要添加它
	// 这样当用户删除 RedisCluster 时，我们就有机会执行清理逻辑

	//	1. kubectl apply -f ... 创建 RedisCluster 对象。
	//	2. Controller 的 watch 检测到新对象，触发 Reconcile。
	//	3. r.Get(...) 成功获取到新资源。
	//	4. redisCluster.DeletionTimestamp 为 nil（因为它不是在被删除）。
	//	5. !controllerutil.ContainsFinalizer(...) 的结果为 `true`，因为新对象还没有 Finalizer。
	//	6. 进入 if 代码块：
	//	    * controllerutil.AddFinalizer(...) 在内存中的 redisCluster 结构体上添加 Finalizer 字符串。
	//	    * r.Update(ctx, redisCluster) 将更新后的对象（现在带有 Finalizer）发送回 API Server。这是最关键的一步。
	//	7. 执行 return ctrl.Result{}, r.Update(...)。第一次 Reconcile 循环到此结束。此时，StatefulSet 还没有被创建。

	// 这会立即为同一个对象触发第二次 Reconcile 循环。
	// 第二次 Reconcile 循环
	//  1. Reconcile 函数再次被调用。
	//  2. r.Get(...) 成功获取资源。这一次，它获取到的 redisCluster 对象已经包含了在第一次循环中添加的 Finalizer。
	//  3. redisCluster.DeletionTimestamp 仍然为 nil。
	//  4. !controllerutil.ContainsFinalizer(...) 的结果现在为 `false`。
	//  5. if 代码块被跳过。
	//  6. 代码继续向下执行。
	//  7. Controller 开始执行 reconcileHeadlessService、reconcileService，并最终执行 reconcileStatefulSet，创建所有需要的子资源

	if !controllerutil.ContainsFinalizer(redisCluster, RedisClusterFinalizer) {
		// 添加 Finalizer 到资源的 metadata.finalizers 列表中
		controllerutil.AddFinalizer(redisCluster, RedisClusterFinalizer)
		// r.Update(ctx, redisCluster) 会触发新的 Reconcile 循环
		// 如果我把代码改成 不return，继续创建statfulset，会出现并发问题吗。如果我在Reconcile 的结尾再掉用 r.Update(ctx, redisCluster) 能避免并发吗
		//   [Finalizer 机制详细解释](../Finalizer_EXPLANATION_GEMINI.MD)
		return ctrl.Result{}, r.Update(ctx, redisCluster)
	}

	// Set initial status if not set
	if redisCluster.Status.Phase == "" {
		// 会不会触发 Reconcile？答案是：通常不会。
		// r.updateStatus(ctx, redisCluster, PhaseCreating, 0, "") 最终会调用 r.Status().Update(...)，它只会更新 status 子资源（即 .status 字段）。
		// controller-runtime 默认只 watch 主资源的 metadata（spec）变化，不会因为 status 字段变化而触发新的 Reconcile。
		// 所以，单独更新 status 字段一般不会导致 Reconcile 再次触发。
		// 只有当 metadata 或 spec 发生变化，或者子资源（如 StatefulSet、Service）变化时，才会再次触发 Reconcile。
		r.updateStatus(ctx, redisCluster, PhaseCreating, 0, "")
	}

	// Reconcile ConfigMap (用于健康检查脚本)
	if err := r.reconcileConfigMap(ctx, redisCluster); err != nil {
		log.Error(err, "Failed to reconcile ConfigMap")
		r.updateStatus(ctx, redisCluster, PhaseFailed, 0, "")
		return ctrl.Result{RequeueAfter: time.Minute * 2}, err
	}

	// Reconcile headless service
	headlessSvcName := fmt.Sprintf("%s-headless", redisCluster.Name)
	if err := r.reconcileHeadlessService(ctx, redisCluster, headlessSvcName); err != nil {
		log.Error(err, "Failed to reconcile headless service")
		r.updateStatus(ctx, redisCluster, PhaseFailed, 0, "")
		return ctrl.Result{RequeueAfter: time.Minute * 2}, err
	}

	// Reconcile service
	if err := r.reconcileService(ctx, redisCluster); err != nil {
		log.Error(err, "Failed to reconcile service")
		r.updateStatus(ctx, redisCluster, PhaseFailed, 0, "")
		return ctrl.Result{RequeueAfter: time.Minute * 2}, err
	}

	// Reconcile statefulset
	if err := r.reconcileStatefulSet(ctx, redisCluster); err != nil {
		log.Error(err, "Failed to reconcile statefulset")
		r.updateStatus(ctx, redisCluster, PhaseFailed, 0, "")
		return ctrl.Result{RequeueAfter: time.Minute * 2}, err
	}

	// 初始化 Redis 集群（仅限 cluster 模式）
	if redisCluster.Spec.Mode == "cluster" {
		if err := r.ensureClusterInitialized(ctx, redisCluster); err != nil {
			log.Error(err, "Failed to ensure cluster initialization")
			// 不设置为 Failed 状态，因为可能只是在等待 Pod 就绪
			return ctrl.Result{RequeueAfter: time.Second * 30}, err
		}

		// 检查集群健康状态并修复故障节点
		if err := r.ensureClusterHealth(ctx, redisCluster); err != nil {
			log.Error(err, "Failed to ensure cluster health")
			// 健康检查失败不阻塞reconcile，但需要重试
			return ctrl.Result{RequeueAfter: time.Minute}, err
		}
	}

	// Update the RedisCluster status
	if err := r.updateRedisClusterStatus(ctx, redisCluster); err != nil {
		log.Error(err, "Failed to update RedisCluster status")
		return ctrl.Result{RequeueAfter: time.Minute * 2}, err
	}

	log.V(1).Info("Successfully reconciled RedisCluster")
	return ctrl.Result{}, nil
}

// validateRedisCluster validates the RedisCluster configuration
func (r *RedisClusterReconciler) validateRedisCluster(redisCluster *dbv1.RedisCluster) error {
	if redisCluster.Spec.Version == "" {
		return fmt.Errorf("redis version is required")
	}

	if redisCluster.Spec.Replicas != nil && *redisCluster.Spec.Replicas < 1 {
		return fmt.Errorf("replicas must be at least 1")
	}

	if redisCluster.Spec.Mode != "" &&
		redisCluster.Spec.Mode != "cluster" &&
		redisCluster.Spec.Mode != "standalone" &&
		redisCluster.Spec.Mode != "sentinel" {
		return fmt.Errorf("invalid mode: %s", redisCluster.Spec.Mode)
	}

	return nil
}

// handleDeletion 处理 RedisCluster 的删除逻辑，确保正确清理所有相关资源
// 这个方法只有在用户执行删除操作时才会被调用（DeletionTimestamp != nil）
func (r *RedisClusterReconciler) handleDeletion(ctx context.Context, redisCluster *dbv1.RedisCluster) (ctrl.Result, error) {
	log := log.FromContext(ctx)

	// 检查是否还有我们的 Finalizer
	// 如果有，说明清理工作还没完成，需要执行清理逻辑
	if controllerutil.ContainsFinalizer(redisCluster, RedisClusterFinalizer) {
		log.Info("开始执行 RedisCluster 清理工作", "name", redisCluster.Name, "namespace", redisCluster.Namespace)

		// ==================== 步骤1: 更新状态为删除中 ====================
		// 让用户知道资源正在被删除，可以通过 kubectl get rediscluster 看到状态
		if err := r.updateStatus(ctx, redisCluster, PhaseDeleting, 0, ""); err != nil {
			log.Error(err, "更新删除状态失败")
			// 即使状态更新失败，也继续执行清理逻辑，不要因为这个阻塞删除流程
		}

		// ==================== 步骤2: 执行实际的资源清理 ====================
		// 清理我们创建的所有子资源（StatefulSet、Service、PVC等）
		// 这是最重要的步骤，确保不留下"孤儿资源"
		if err := r.cleanupOwnedResources(ctx, redisCluster); err != nil {
			log.Error(err, "清理子资源失败，将重试")
			// 如果清理失败，返回错误并设置重试
			// Controller 会在1分钟后重新调用这个方法
			return ctrl.Result{RequeueAfter: time.Minute}, err
		}

		// ==================== 步骤3: 移除 Finalizer ====================
		// 清理工作完成后，移除我们的 Finalizer
		// 这告诉 Kubernetes："我们的清理工作完成了，可以真正删除这个资源了"
		controllerutil.RemoveFinalizer(redisCluster, RedisClusterFinalizer)

		// 更新资源，移除 Finalizer
		// 这个更新操作完成后，API Server 会检测到 Finalizer 列表为空
		// 然后真正从 etcd 中删除 RedisCluster 资源
		if err := r.Update(ctx, redisCluster); err != nil {
			log.Error(err, "移除 Finalizer 失败")
			return ctrl.Result{}, err
		}

		log.Info("RedisCluster 清理工作完成", "name", redisCluster.Name)
	}

	// 如果没有我们的 Finalizer，说明清理工作已经完成
	// 或者这个 RedisCluster 从来没有添加过我们的 Finalizer
	return ctrl.Result{}, nil
}

// cleanupOwnedResources 清理 RedisCluster 拥有的所有子资源
// 这个方法负责清理我们在 Reconcile 过程中创建的所有 Kubernetes 资源
func (r *RedisClusterReconciler) cleanupOwnedResources(ctx context.Context, redisCluster *dbv1.RedisCluster) error {
	log := log.FromContext(ctx)

	// ==================== 重要说明：OwnerReference 机制 ====================
	// 在我们的代码中，创建 StatefulSet 和 Service 时都调用了：
	//   ctrl.SetControllerReference(redisCluster, sts, r.Scheme)
	//   ctrl.SetControllerReference(redisCluster, svc, r.Scheme)
	//
	// 这会在子资源上设置 OwnerReference 字段，指向我们的 RedisCluster
	// 当 RedisCluster 被删除时，Kubernetes 会自动删除所有设置了 OwnerReference 的子资源
	// 这包括：
	//   - StatefulSet (以及 StatefulSet 管理的 Pod)
	//   - Service (包括 headless service)
	//   - PVC (通过 StatefulSet 的 VolumeClaimTemplate 创建的)
	//
	// 所以大部分情况下，我们不需要手动删除这些资源
	// Kubernetes 会自动处理，这叫做"级联删除"

	// ==================== 自定义清理逻辑 ====================
	// 但是，有些清理工作 Kubernetes 无法自动完成，需要我们手动处理：
	// 1. 备份数据到外部存储
	// 2. 清理外部资源（比如云服务商的负载均衡器、DNS记录等）
	// 3. 发送通知（比如告诉监控系统这个 Redis 集群已经下线）
	// 4. 清理不受 OwnerReference 保护的资源

	// 示例：如果需要在删除前备份数据
	// if err := r.backupRedisData(ctx, redisCluster); err != nil {
	//     log.Error(err, "备份 Redis 数据失败")
	//     return err
	// }

	// 示例：如果需要清理外部资源
	// if err := r.cleanupExternalResources(ctx, redisCluster); err != nil {
	//     log.Error(err, "清理外部资源失败")
	//     return err
	// }

	log.V(1).Info("RedisCluster 自定义清理逻辑执行完成", "name", redisCluster.Name)
	return nil
}

// updateStatus 更新 RedisCluster 的状态信息
// 状态信息会显示在 kubectl get rediscluster 的输出中，让用户了解集群当前状态
func (r *RedisClusterReconciler) updateStatus(ctx context.Context, redisCluster *dbv1.RedisCluster, phase string, readyReplicas int32, master string) error {
	// 更新状态字段
	redisCluster.Status.Phase = phase                 // 当前阶段：Creating/Running/Updating/Deleting/Failed
	redisCluster.Status.ReadyReplicas = readyReplicas // 就绪的副本数
	redisCluster.Status.Master = master               // 主节点名称

	// 调用 Status().Update() 而不是 Update()
	// Status().Update() 只更新 status 字段，不会触发新的 Reconcile
	// Update() 会更新整个资源，可能触发不必要的 Reconcile 循环
	return r.Status().Update(ctx, redisCluster)
}

// reconcileHeadlessService 协调 Headless Service（用于 StatefulSet）
// Headless Service 的作用：
// 1. 为 StatefulSet 中的每个 Pod 提供稳定的网络标识
// 2. Pod 可以通过 <pod-name>.<service-name>.<namespace>.svc.cluster.local 访问
// 3. 例如：redis-sample-0.redis-sample-headless.default.svc.cluster.local
func (r *RedisClusterReconciler) reconcileHeadlessService(ctx context.Context, redisCluster *dbv1.RedisCluster, serviceName string) error {
	log := log.FromContext(ctx)

	// ==================== 步骤1: 检查 Headless Service 是否存在 ====================
	headlessSvc := &corev1.Service{}
	err := r.Get(ctx, types.NamespacedName{Name: serviceName, Namespace: redisCluster.Namespace}, headlessSvc)

	if err != nil && errors.IsNotFound(err) {
		// ==================== 情况1: Service 不存在，需要创建 ====================
		headlessSvc = r.serviceForRedis(redisCluster, serviceName, true) // true 表示创建 headless service
		log.Info("创建新的 Headless Service", "Service.Namespace", headlessSvc.Namespace, "Service.Name", headlessSvc.Name)
		return r.Create(ctx, headlessSvc)
	} else if err != nil {
		// ==================== 情况2: 获取 Service 时发生其他错误 ====================
		return err
	}

	// ==================== 情况3: Service 已存在，检查是否需要更新 ====================
	// 比较当前 Service 配置与期望配置是否一致
	desired := r.serviceForRedis(redisCluster, serviceName, true)

	// 使用 reflect.DeepEqual 深度比较两个对象是否相等
	// 如果端口配置或选择器发生变化，需要更新 Service
	if !reflect.DeepEqual(headlessSvc.Spec.Ports, desired.Spec.Ports) ||
		!reflect.DeepEqual(headlessSvc.Spec.Selector, desired.Spec.Selector) {

		// 更新 Service 配置
		headlessSvc.Spec.Ports = desired.Spec.Ports
		headlessSvc.Spec.Selector = desired.Spec.Selector
		log.Info("更新 Headless Service 配置", "Service.Namespace", headlessSvc.Namespace, "Service.Name", headlessSvc.Name)
		return r.Update(ctx, headlessSvc)
	}

	// Service 配置正确，无需更新
	return nil
}

// reconcileConfigMap 协调 ConfigMap（包含健康检查脚本）
func (r *RedisClusterReconciler) reconcileConfigMap(ctx context.Context, redisCluster *dbv1.RedisCluster) error {
	log := log.FromContext(ctx)

	// ConfigMap 名称
	configMapName := fmt.Sprintf("%s-scripts", redisCluster.Name)
	cm := &corev1.ConfigMap{}
	err := r.Get(ctx, types.NamespacedName{Name: configMapName, Namespace: redisCluster.Namespace}, cm)

	if err != nil && errors.IsNotFound(err) {
		// ConfigMap 不存在，需要创建
		cm = r.configMapForRedis(redisCluster)
		log.Info("创建新的 ConfigMap", "ConfigMap.Namespace", cm.Namespace, "ConfigMap.Name", cm.Name)
		return r.Create(ctx, cm)
	} else if err != nil {
		return err
	}

	// ConfigMap 已存在，检查是否需要更新
	desired := r.configMapForRedis(redisCluster)
	if !reflect.DeepEqual(cm.Data, desired.Data) {
		cm.Data = desired.Data
		log.Info("更新 ConfigMap", "ConfigMap.Namespace", cm.Namespace, "ConfigMap.Name", cm.Name)
		return r.Update(ctx, cm)
	}

	return nil
}

// configMapForRedis 创建包含脚本的 ConfigMap
func (r *RedisClusterReconciler) configMapForRedis(m *dbv1.RedisCluster) *corev1.ConfigMap {
	ls := labelsForRedis(m.Name)
	cm := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-scripts", m.Name),
			Namespace: m.Namespace,
			Labels:    ls,
		},
		Data: map[string]string{
			ReadinessScriptKey: getReadinessScript(),
		},
	}
	ctrl.SetControllerReference(m, cm, r.Scheme)
	return cm
}

// reconcileService 协调普通 Service（用于外部访问）
// 普通 Service 的作用：
// 1. 为外部客户端提供统一的访问入口
// 2. 负载均衡到后端的 Redis Pod
// 3. 客户端可以通过 <service-name>.<namespace>.svc.cluster.local:6379 访问
func (r *RedisClusterReconciler) reconcileService(ctx context.Context, redisCluster *dbv1.RedisCluster) error {
	log := log.FromContext(ctx)

	// ==================== 步骤1: 检查普通 Service 是否存在 ====================
	svc := &corev1.Service{}
	err := r.Get(ctx, types.NamespacedName{Name: redisCluster.Name, Namespace: redisCluster.Namespace}, svc)

	if err != nil && errors.IsNotFound(err) {
		// ==================== 情况1: Service 不存在，需要创建 ====================
		svc = r.serviceForRedis(redisCluster, redisCluster.Name, false) // false 表示创建普通 service
		log.Info("创建新的 Service", "Service.Namespace", svc.Namespace, "Service.Name", svc.Name)
		return r.Create(ctx, svc)
	} else if err != nil {
		// ==================== 情况2: 获取 Service 时发生其他错误 ====================
		return err
	}

	// ==================== 情况3: Service 已存在，检查是否需要更新 ====================
	// 比较当前 Service 配置与期望配置是否一致
	desired := r.serviceForRedis(redisCluster, redisCluster.Name, false)

	// 检查端口配置和选择器是否发生变化
	if !reflect.DeepEqual(svc.Spec.Ports, desired.Spec.Ports) ||
		!reflect.DeepEqual(svc.Spec.Selector, desired.Spec.Selector) {

		// 更新 Service 配置
		svc.Spec.Ports = desired.Spec.Ports
		svc.Spec.Selector = desired.Spec.Selector
		log.Info("更新 Service 配置", "Service.Namespace", svc.Namespace, "Service.Name", svc.Name)
		return r.Update(ctx, svc)
	}

	// Service 配置正确，无需更新
	return nil
}

// reconcileStatefulSet 协调 StatefulSet（Redis 集群的核心组件）
// StatefulSet 的作用：
// 1. 管理有状态的 Pod（每个 Pod 有固定的名称和存储）
// 2. 提供有序的部署和扩缩容
// 3. 每个 Pod 都有独立的 PVC（持久化存储）
// 4. Pod 名称固定：redis-sample-0, redis-sample-1, redis-sample-2
func (r *RedisClusterReconciler) reconcileStatefulSet(ctx context.Context, redisCluster *dbv1.RedisCluster) error {
	log := log.FromContext(ctx)

	// ==================== 步骤1: 检查 StatefulSet 是否存在 ====================
	sts := &appsv1.StatefulSet{}
	err := r.Get(ctx, types.NamespacedName{Name: redisCluster.Name, Namespace: redisCluster.Namespace}, sts)

	if err != nil && errors.IsNotFound(err) {
		// ==================== 情况1: StatefulSet 不存在，需要创建 ====================
		sts = r.statefulSetForRedis(redisCluster)
		log.Info("创建新的 StatefulSet", "StatefulSet.Namespace", sts.Namespace, "StatefulSet.Name", sts.Name)
		return r.Create(ctx, sts)
	} else if err != nil {
		// ==================== 情况2: 获取 StatefulSet 时发生其他错误 ====================
		return err
	}

	// ==================== 情况3: StatefulSet 已存在，检查是否需要更新 ====================
	// 比较当前 StatefulSet 配置与期望配置是否一致
	desired := r.statefulSetForRedis(redisCluster)

	// 检查用户是否尝试修改存储大小（这是不允许的）
	if len(sts.Spec.VolumeClaimTemplates) > 0 && len(desired.Spec.VolumeClaimTemplates) > 0 {
		currentStorage := sts.Spec.VolumeClaimTemplates[0].Spec.Resources.Requests[corev1.ResourceStorage]
		desiredStorage := desired.Spec.VolumeClaimTemplates[0].Spec.Resources.Requests[corev1.ResourceStorage]
		if !currentStorage.Equal(desiredStorage) {
			log.Info("WARNING: 检测到存储大小变更尝试，但 StatefulSet 的 VolumeClaimTemplates 是不可变的",
				"current", currentStorage.String(),
				"desired", desiredStorage.String(),
				"StatefulSet.Name", sts.Name)
			// TODO: 可以在这里更新 RedisCluster 的状态，添加警告信息
		}
	}

	// 检查关键配置是否发生变化：
	// 1. 副本数（replicas）- 用户可能修改了 spec.replicas
	// 2. 容器配置（containers）- Redis 版本、启动参数等
	// 注意：VolumeClaimTemplates 是不可变字段，创建后不能修改
	if !reflect.DeepEqual(sts.Spec.Replicas, desired.Spec.Replicas) ||
		!reflect.DeepEqual(sts.Spec.Template.Spec.Containers, desired.Spec.Template.Spec.Containers) {

		// 更新 StatefulSet 配置（仅更新可变字段）
		sts.Spec.Replicas = desired.Spec.Replicas
		sts.Spec.Template = desired.Spec.Template
		// 不更新 VolumeClaimTemplates，这是不可变字段
		log.Info("更新 StatefulSet 配置", "StatefulSet.Namespace", sts.Namespace, "StatefulSet.Name", sts.Name)
		return r.Update(ctx, sts)
	}

	// StatefulSet 配置正确，无需更新
	return nil
}

// updateRedisClusterStatus 根据当前实际状态更新 RedisCluster 的状态信息
// 这个方法会检查所有 Redis Pod 的状态，计算就绪副本数，并更新 RedisCluster 的状态
func (r *RedisClusterReconciler) updateRedisClusterStatus(ctx context.Context, redisCluster *dbv1.RedisCluster) error {
	// ==================== 步骤1: 获取所有相关的 Pod ====================
	// 查找所有属于这个 RedisCluster 的 Pod
	podList := &corev1.PodList{}
	listOpts := []client.ListOption{
		client.InNamespace(redisCluster.Namespace),               // 限制在同一个命名空间
		client.MatchingLabels(labelsForRedis(redisCluster.Name)), // 通过标签选择器找到相关 Pod
	}
	// 实际执行: GET /api/v1/namespaces/{namespace}/pods?labelSelector=app=redis-cluster,redis-cluster_cr={name}
	if err := r.List(ctx, podList, listOpts...); err != nil {
		return err
	}

	// ==================== 步骤2: 计算就绪副本数（修正：按 Pod 计数，不是按容器） ====================
	var readyReplicas int32
	var masterPod string

	// 遍历所有 Pod，检查每个 Pod 是否就绪
	for _, pod := range podList.Items {
		if isPodReady(&pod) {
			readyReplicas++ // 这里按 Pod 计数，不是按容器计数

			// 简单起见，将第一个就绪的 Pod 作为主节点
			// 在真实的 Redis 集群中，需要通过 Redis 命令来确定真正的主节点
			if masterPod == "" {
				masterPod = pod.Name
			}
		}
	}

	// ==================== 步骤3: 根据实际情况确定集群阶段 ====================
	phase := PhaseRunning // 默认为运行状态

	if readyReplicas == 0 {
		// 没有就绪的 Pod，说明集群正在创建中
		phase = PhaseCreating
	} else if redisCluster.Spec.Replicas != nil && readyReplicas < *redisCluster.Spec.Replicas {
		// 就绪副本数少于期望副本数，说明正在更新中（扩容、升级等）
		phase = PhaseUpdating
	}
	// 如果 readyReplicas == *redisCluster.Spec.Replicas，则保持 PhaseRunning

	// ==================== 步骤4: 只有状态发生变化时才更新 ====================
	// 避免不必要的 API 调用，只有当状态真正发生变化时才更新
	if redisCluster.Status.ReadyReplicas != readyReplicas ||
		redisCluster.Status.Phase != phase ||
		redisCluster.Status.Master != masterPod {

		// 调用 updateStatus 方法更新状态
		return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
	}

	// 状态没有变化，无需更新
	return nil
}

// isPodReady 检查单个 Pod 是否处于就绪状态
// Pod 就绪的条件：Pod 的 Ready 条件为 True
func isPodReady(pod *corev1.Pod) bool {
	// 遍历 Pod 的所有状态条件
	for _, condition := range pod.Status.Conditions {
		// 检查是否有 PodReady 类型的条件，且状态为 True
		if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
			return true
		}
	}
	// 没有找到 Ready=True 的条件，说明 Pod 还没有就绪
	return false
}

func (r *RedisClusterReconciler) serviceForRedis(m *dbv1.RedisCluster, name string, headless bool) *corev1.Service {
	ls := labelsForRedis(m.Name)
	svc := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: m.Namespace,
			Labels:    ls,
		},
		Spec: corev1.ServiceSpec{
			Ports: []corev1.ServicePort{{
				Port:       DefaultRedisPort,
				TargetPort: intstr.FromInt(DefaultRedisPort),
				Name:       "redis",
			}},
			Selector: ls,
		},
	}
	if headless {
		svc.Spec.ClusterIP = "None"
	}
	ctrl.SetControllerReference(m, svc, r.Scheme)
	return svc
}

func (r *RedisClusterReconciler) statefulSetForRedis(m *dbv1.RedisCluster) *appsv1.StatefulSet {
	ls := labelsForRedis(m.Name)
	replicas := m.Spec.Replicas
	if replicas == nil {
		three := int32(3)
		replicas = &three
	}

	// Determine storage size
	storageSize := DefaultStorageSize
	if m.Spec.Storage.Size != "" {
		storageSize = m.Spec.Storage.Size
	}

	// Create container with volume mounts
	container := corev1.Container{
		Image: "redis:" + m.Spec.Version,
		Name:  "redis",
		Ports: []corev1.ContainerPort{{
			ContainerPort: DefaultRedisPort,
			Name:          "redis",
		}},
		VolumeMounts: []corev1.VolumeMount{
			{
				Name:      "redis-data",
				MountPath: "/data",
			},
			{
				Name:      "scripts",
				MountPath: "/scripts",
			},
		},
		// Add Redis configuration for persistence
		Command: r.generateRedisCommand(r.getRedisMode(m)),
		// 环境变量
		Env: []corev1.EnvVar{
			{
				Name:  "REDIS_MODE",
				Value: r.getRedisMode(m),
			},
		},
		// 资源限制
		Resources: corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("128Mi"),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("1000m"),
				corev1.ResourceMemory: resource.MustParse("1Gi"),
			},
		},
		// 存活探针 - 检查 Redis 进程是否存活
		LivenessProbe: &corev1.Probe{
			ProbeHandler: corev1.ProbeHandler{
				Exec: &corev1.ExecAction{
					Command: []string{"redis-cli", "-h", "127.0.0.1", "ping"},
				},
			},
			InitialDelaySeconds: 30,
			TimeoutSeconds:      5,
			PeriodSeconds:       10,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		},
		// 就绪探针 - 使用脚本进行智能检查
		ReadinessProbe: &corev1.Probe{
			ProbeHandler: corev1.ProbeHandler{
				Exec: &corev1.ExecAction{
					Command: []string{"/bin/bash", "/scripts/readiness.sh"},
				},
			},
			InitialDelaySeconds: 10,
			TimeoutSeconds:      5,
			PeriodSeconds:       5,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		},
	}

	sts := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      m.Name,
			Namespace: m.Namespace,
			Labels:    ls,
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas:    replicas,
			ServiceName: fmt.Sprintf("%s-headless", m.Name),
			Selector: &metav1.LabelSelector{
				MatchLabels: ls,
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: ls,
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{container},
					// Pod security context
					SecurityContext: &corev1.PodSecurityContext{
						RunAsNonRoot: &[]bool{true}[0],
						RunAsUser:    &[]int64{999}[0],
						RunAsGroup:   &[]int64{999}[0],
						FSGroup:      &[]int64{999}[0],
					},
					// Add volumes
					Volumes: []corev1.Volume{
						{
							Name: "scripts",
							VolumeSource: corev1.VolumeSource{
								ConfigMap: &corev1.ConfigMapVolumeSource{
									LocalObjectReference: corev1.LocalObjectReference{
										Name: fmt.Sprintf("%s-scripts", m.Name),
									},
									DefaultMode: &[]int32{0755}[0],
								},
							},
						},
					},
				},
			},
			VolumeClaimTemplates: []corev1.PersistentVolumeClaim{{
				ObjectMeta: metav1.ObjectMeta{
					Name: "redis-data",
				},
				Spec: corev1.PersistentVolumeClaimSpec{
					AccessModes: []corev1.PersistentVolumeAccessMode{
						corev1.ReadWriteOnce,
					},
					Resources: corev1.VolumeResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceStorage: resource.MustParse(storageSize),
						},
					},
				},
			}},
		},
	}

	ctrl.SetControllerReference(m, sts, r.Scheme)
	return sts
}

func labelsForRedis(name string) map[string]string {
	return map[string]string{"app": "redis-cluster", "redis-cluster_cr": name}
}

// getRedisMode 获取 Redis 模式，如果未设置则返回默认值
func (r *RedisClusterReconciler) getRedisMode(m *dbv1.RedisCluster) string {
	if m.Spec.Mode == "" {
		return "cluster" // 默认值
	}
	return m.Spec.Mode
}

// generateRedisCommand 根据模式生成 Redis 启动命令
func (r *RedisClusterReconciler) generateRedisCommand(mode string) []string {
	baseCmd := []string{
		"redis-server",
		"--appendonly", "yes",
		"--appendfsync", "everysec",
	}

	switch mode {
	case "cluster":
		// 集群模式需要额外的参数
		clusterCmd := append(baseCmd,
			"--cluster-enabled", "yes",
			"--cluster-config-file", "/data/nodes.conf",
			"--cluster-node-timeout", "5000",
		)
		return clusterCmd
	case "sentinel":
		// 哨兵模式的特殊配置
		return baseCmd
	default:
		// standalone 模式
		return baseCmd
	}
}

// getReadinessScript 返回健康检查脚本内容
func getReadinessScript() string {
	return `#!/bin/bash
set -e

# 检查 Redis 是否响应
redis-cli -h 127.0.0.1 ping > /dev/null || exit 1

# 对于非集群模式，ping 成功就够了
if [ "$REDIS_MODE" != "cluster" ]; then
    exit 0
fi

# 集群模式：检查是否已加入集群
cluster_info=$(redis-cli -h 127.0.0.1 cluster info 2>/dev/null || true)

# 如果还没有集群信息，说明还在等待初始化
if [ -z "$cluster_info" ] || echo "$cluster_info" | grep -q "ERR This instance has cluster support disabled"; then
    # Redis 已启动但集群未初始化，这是正常的
    exit 0
fi

# 如果已经有集群信息，检查集群状态
echo "$cluster_info" | grep -q "cluster_state:ok" || exit 1
`
}

// isClusterInitialized 检查集群是否已经初始化
func (r *RedisClusterReconciler) isClusterInitialized(ctx context.Context, redisCluster *dbv1.RedisCluster) (bool, error) {
	// 检查 annotation 标记
	if redisCluster.Annotations != nil {
		if val, ok := redisCluster.Annotations[ClusterInitializedAnnotation]; ok && val == "true" {
			return true, nil
		}
	}
	return false, nil
}

// ensureClusterInitialized 确保集群已初始化
func (r *RedisClusterReconciler) ensureClusterInitialized(ctx context.Context, redisCluster *dbv1.RedisCluster) error {
	log := log.FromContext(ctx)

	// 检查是否已初始化
	initialized, err := r.isClusterInitialized(ctx, redisCluster)
	if err != nil {
		return err
	}
	if initialized {
		return nil
	}

	// 获取所有 Pod
	podList := &corev1.PodList{}
	listOpts := []client.ListOption{
		client.InNamespace(redisCluster.Namespace),
		client.MatchingLabels(labelsForRedis(redisCluster.Name)),
	}
	if err := r.List(ctx, podList, listOpts...); err != nil {
		return err
	}

	// 检查期望的副本数
	expectedReplicas := int32(3)
	if redisCluster.Spec.Replicas != nil {
		expectedReplicas = *redisCluster.Spec.Replicas
	}

	// 等待所有 Pod Ready
	readyPods := 0
	for _, pod := range podList.Items {
		if isPodReady(&pod) {
			readyPods++
		}
	}

	if int32(readyPods) < expectedReplicas {
		log.Info("等待所有 Pod 就绪以进行集群初始化",
			"ready", readyPods, "expected", expectedReplicas)
		return nil // 不是错误，下次 reconcile 再检查
	}

	// 所有 Pod 都 Ready，执行集群初始化
	log.Info("所有 Pod 已就绪，开始初始化 Redis 集群")
	if err := r.initializeCluster(ctx, redisCluster, podList.Items); err != nil {
		return fmt.Errorf("初始化集群失败: %w", err)
	}

	// 标记集群已初始化
	if redisCluster.Annotations == nil {
		redisCluster.Annotations = make(map[string]string)
	}
	redisCluster.Annotations[ClusterInitializedAnnotation] = "true"
	if err := r.Update(ctx, redisCluster); err != nil {
		return fmt.Errorf("更新集群初始化标记失败: %w", err)
	}

	log.Info("Redis 集群初始化完成")
	return nil
}

// execRedisCommand 在指定Pod中执行Redis命令
func (r *RedisClusterReconciler) execRedisCommand(ctx context.Context, podName, namespace string, command []string) (string, error) {
	// 在集群内部运行时，ServiceAccount token 来自于 Kubernetes 自动挂载的 Secret：
	// - Token 文件路径：/var/run/secrets/kubernetes.io/serviceaccount/token
	// - CA 证书路径：/var/run/secrets/kubernetes.io/serviceaccount/ca.crt
	// - Namespace 文件：/var/run/secrets/kubernetes.io/serviceaccount/namespace
	//
	// ctrl.GetConfigOrDie() 会自动处理以下配置优先级：
	// 1. --kubeconfig 命令行参数指定的文件
	// 2. KUBECONFIG 环境变量指定的文件
	// 3. 集群内配置（使用 ServiceAccount token）
	// 4. ~/.kube/config 文件
	config := ctrl.GetConfigOrDie()
	// 这个 token 来自于 Kubernetes 为 Pod 自动创建的 ServiceAccount：
	// 1. 每个 Pod 都有一个关联的 ServiceAccount（默认是 "default"）
	// 2. 这个 ServiceAccount 的 token 被自动挂载到 Pod 的文件系统中
	// 3. 该 token 的权限由 RBAC（Role-Based Access Control）决定
	//
	// 权限控制机制：
	// - 不是所有 Pod 都能执行任意命令，权限由 ServiceAccount 绑定的 Role/ClusterRole 决定
	// - 这个 Controller Pod 需要有执行 "pods/exec" 子资源的权限
	// - 通常在部署时会创建对应的 ClusterRole 和 ClusterRoleBinding
	// - 例如：kubectl create clusterrolebinding redis-controller --clusterrole=cluster-admin --serviceaccount=default:default
	//
	// 安全考虑：
	// - 生产环境中应该使用最小权限原则，只授予必要的权限
	// - 可以创建专用的 ServiceAccount 和受限的 Role，而不是使用 cluster-admin
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return "", fmt.Errorf("failed to create clientset: %w", err)
	}

	req := clientset.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(namespace).
		SubResource("exec")

	req.VersionedParams(&corev1.PodExecOptions{
		Command: command,
		Stdout:  true,
		Stderr:  true,
	}, scheme.ParameterCodec)

	exec, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
	if err != nil {
		return "", fmt.Errorf("failed to create executor: %w", err)
	}

	var stdout, stderr bytes.Buffer
	err = exec.Stream(remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
	})

	if err != nil {
		return "", fmt.Errorf("command failed: %w, stderr: %s", err, stderr.String())
	}

	return stdout.String(), nil
}

// initializeCluster 执行集群初始化
func (r *RedisClusterReconciler) initializeCluster(ctx context.Context, redisCluster *dbv1.RedisCluster, pods []corev1.Pod) error {
	log := log.FromContext(ctx)

	// 构建节点列表
	nodes := []string{}
	for _, pod := range pods {
		// 使用 headless service 的 DNS 名称
		nodes = append(nodes, fmt.Sprintf("%s.%s-headless.%s.svc.cluster.local:%d",
			pod.Name, redisCluster.Name, redisCluster.Namespace, DefaultRedisPort))
	}

	// 修复副本配置逻辑 - 只支持3节点和6节点
	replicas := 0
	nodeCount := len(nodes)
	if nodeCount == 6 {
		replicas = 1 // 6节点: 3主3从
	} else if nodeCount == 3 {
		replicas = 0 // 3节点: 3主0从
	} else {
		return fmt.Errorf("不支持的节点数量: %d, 仅支持3或6个节点的cluster模式", nodeCount)
	}

	// 构建集群创建命令
	createCmd := []string{
		"redis-cli", "--cluster", "create",
	}
	createCmd = append(createCmd, nodes...)
	createCmd = append(createCmd, "--cluster-replicas", fmt.Sprintf("%d", replicas), "--cluster-yes")

	log.Info("执行集群创建命令", "command", strings.Join(createCmd, " "), "nodes", nodeCount, "replicas", replicas)

	// 🔥 关键修复: 实际执行集群创建命令
	if len(pods) > 0 {
		output, err := r.execRedisCommand(ctx, pods[0].Name, redisCluster.Namespace, createCmd)
		if err != nil {
			return fmt.Errorf("集群创建失败: %w", err)
		}
		log.Info("集群创建成功", "output", output)
	}

	// 验证集群状态
	return r.verifyClusterStatus(ctx, redisCluster, pods)
}

// verifyClusterStatus 验证集群状态
func (r *RedisClusterReconciler) verifyClusterStatus(ctx context.Context, redisCluster *dbv1.RedisCluster, pods []corev1.Pod) error {
	log := log.FromContext(ctx)

	for _, pod := range pods {
		// 检查每个节点的集群状态
		output, err := r.execRedisCommand(ctx, pod.Name, redisCluster.Namespace,
			[]string{"redis-cli", "cluster", "nodes"})
		if err != nil {
			return fmt.Errorf("验证节点 %s 集群状态失败: %w", pod.Name, err)
		}

		// 检查输出是否包含集群信息
		if !strings.Contains(output, "master") && !strings.Contains(output, "slave") {
			return fmt.Errorf("节点 %s 不在集群中", pod.Name)
		}

		log.V(1).Info("节点集群状态验证通过", "pod", pod.Name)
	}

	// 检查集群整体状态
	if len(pods) > 0 {
		clusterInfo, err := r.execRedisCommand(ctx, pods[0].Name, redisCluster.Namespace,
			[]string{"redis-cli", "cluster", "info"})
		if err != nil {
			return fmt.Errorf("获取集群信息失败: %w", err)
		}

		if !strings.Contains(clusterInfo, "cluster_state:ok") {
			return fmt.Errorf("集群状态不正常: %s", clusterInfo)
		}
	}

	log.Info("Redis集群状态验证通过")
	return nil
}

// ensureClusterHealth 检查并修复集群健康状态
func (r *RedisClusterReconciler) ensureClusterHealth(ctx context.Context, redisCluster *dbv1.RedisCluster) error {
	log := log.FromContext(ctx)

	// 获取所有Pod
	podList := &corev1.PodList{}
	listOpts := []client.ListOption{
		client.InNamespace(redisCluster.Namespace),
		client.MatchingLabels(labelsForRedis(redisCluster.Name)),
	}
	if err := r.List(ctx, podList, listOpts...); err != nil {
		return err
	}

	// 检查就绪的Pod数量
	readyPods := []corev1.Pod{}
	for _, pod := range podList.Items {
		if isPodReady(&pod) {
			readyPods = append(readyPods, pod)
		}
	}

	if len(readyPods) == 0 {
		log.Info("没有就绪的Pod，跳过集群健康检查")
		return nil
	}

	// 检查集群完整性
	healthyNodes := 0
	var healthyPod *corev1.Pod

	for _, pod := range readyPods {
		clusterInfo, err := r.execRedisCommand(ctx, pod.Name, redisCluster.Namespace,
			[]string{"redis-cli", "cluster", "info"})
		if err == nil && strings.Contains(clusterInfo, "cluster_state:ok") {
			healthyNodes++
			if healthyPod == nil {
				healthyPod = &pod
			}
		}
	}

	// 如果集群不健康，尝试修复
	if healthyNodes < len(readyPods) && healthyPod != nil {
		log.Info("检测到集群节点故障，尝试修复", "healthy", healthyNodes, "total", len(readyPods))
		return r.repairClusterNodes(ctx, redisCluster, readyPods, healthyPod)
	}

	return nil
}

// repairClusterNodes 修复故障的集群节点
func (r *RedisClusterReconciler) repairClusterNodes(ctx context.Context, redisCluster *dbv1.RedisCluster, pods []corev1.Pod, healthyPod *corev1.Pod) error {
	log := log.FromContext(ctx)

	for _, pod := range pods {
		if pod.Name == healthyPod.Name {
			continue // 跳过健康节点
		}

		// 检查节点是否需要修复
		clusterInfo, err := r.execRedisCommand(ctx, pod.Name, redisCluster.Namespace,
			[]string{"redis-cli", "cluster", "info"})
		if err != nil || !strings.Contains(clusterInfo, "cluster_state:ok") {
			log.Info("修复故障节点", "pod", pod.Name)

			// 尝试重置节点并重新加入集群
			if err := r.repairSingleNode(ctx, redisCluster, &pod, healthyPod); err != nil {
				log.Error(err, "修复节点失败", "pod", pod.Name)
				// 继续尝试修复其他节点
			}
		}
	}

	return nil
}

// repairSingleNode 修复单个故障节点
func (r *RedisClusterReconciler) repairSingleNode(ctx context.Context, redisCluster *dbv1.RedisCluster, faultPod, healthyPod *corev1.Pod) error {
	log := log.FromContext(ctx)

	// 1. 重置故障节点的集群配置
	_, err := r.execRedisCommand(ctx, faultPod.Name, redisCluster.Namespace,
		[]string{"redis-cli", "cluster", "reset"})
	if err != nil {
		log.V(1).Info("重置集群配置失败，继续尝试", "pod", faultPod.Name, "error", err)
	}

	// 2. 从健康节点添加故障节点回集群
	nodeAddress := fmt.Sprintf("%s.%s-headless.%s.svc.cluster.local:%d",
		faultPod.Name, redisCluster.Name, redisCluster.Namespace, DefaultRedisPort)

	addNodeCmd := []string{"redis-cli", "--cluster", "add-node", nodeAddress,
		fmt.Sprintf("%s.%s-headless.%s.svc.cluster.local:%d",
			healthyPod.Name, redisCluster.Name, redisCluster.Namespace, DefaultRedisPort)}

	output, err := r.execRedisCommand(ctx, healthyPod.Name, redisCluster.Namespace, addNodeCmd)
	if err != nil {
		return fmt.Errorf("添加节点到集群失败: %w", err)
	}

	log.Info("节点修复成功", "pod", faultPod.Name, "output", output)
	return nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *RedisClusterReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&dbv1.RedisCluster{}).
		Owns(&appsv1.StatefulSet{}).
		Owns(&corev1.Service{}).
		Owns(&corev1.ConfigMap{}).
		Owns(&corev1.PersistentVolumeClaim{}).
		WithOptions(controller.Options{
			MaxConcurrentReconciles: 1, // 控制并发为1
		}).
		Complete(r)
}
