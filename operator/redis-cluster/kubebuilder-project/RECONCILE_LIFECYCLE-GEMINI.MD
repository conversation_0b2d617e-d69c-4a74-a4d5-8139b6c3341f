
# RedisCluster Reconcile 生命周期全解析

本文档详细描述了当用户 `kubectl apply` 一个 `RedisCluster` 资源时，其 Controller 的 `Reconcile` 函数在不同场景下的触发过程和行为。

---

## 核心概念

在深入细节之前，我们先明确几个关键角色的职责：

*   **Kubernetes API Server**: 接收和存储所有资源对象（包括我们的 `RedisCluster` CR）到 etcd 的中心组件。
*   **Controller-Runtime 框架**: Operator 的底层框架。它负责：
    1.  通过 `Informer` 机制监听（Watch）API Server 的资源变化事件（增、删、改）。
    2.  维护一个本地缓存（Cache）以减少对 API Server 的直接请求。
    3.  管理一个工作队列（Work Queue），将需要处理的资源键（`namespace/name`）放入队列。
    4.  从队列中取出资源键，调用我们编写的 `Reconcile` 函数。
    5.  根据 `Reconcile` 的返回结果（`ctrl.Result`, `error`）决定是否以及何时将资源键重新放回队列进行重试。
*   **我们编写的 `Reconcile` 函数**: 包含核心业务逻辑。其目标是检查当前状态与期望状态的差异，并执行操作（创建/更新/删除子资源）来使两者一致。
*   **其他 Controller (例如 StatefulSet Controller)**: Kubernetes 内置的控制器，负责管理像 StatefulSet 这样的原生资源。

---

## 阶段一：首次 `apply`

这是最复杂的阶段，涉及从无到有的完整创建过程。假设集群中还没有任何关于 `user-session-cache` 的资源。

### 1. Reconcile 触发次数

**结论**: **大约 5 到 8 次**，甚至更多。这不是一个固定的数字，它取决于 API Server 的响应速度和 Pod 的启动速度。它是一个动态的、由多个事件驱动的收敛过程。

### 2. 触发 Reconcile 的逻辑

`Reconcile` 的触发主要来自两个方面：

1.  **我们代码的主动行为**:
    *   在代码中调用 `r.Update(ctx, redisCluster)` 或 `r.Status().Update(ctx, redisCluster)` 会修改 `RedisCluster` 资源本身，这会立即产生一个“UPDATE”事件，被框架捕获后触发下一次 `Reconcile`。

2.  **Controller-Runtime 框架的监听机制**:
    *   **监听主资源**: `For(&dbv1.RedisCluster{})` 设置了对 `RedisCluster` 资源的监听。`kubectl apply` 创建资源时，会触发一次 `Reconcile`。
    *   **监听子资源**: `Owns(&appsv1.StatefulSet{})` 和 `Owns(&corev1.Service{})` 是关键。它告诉框架：“如果这个 Controller 拥有的（Owner）StatefulSet 或 Service 发生了任何变化（包括被创建、更新，尤其是其 `status` 字段的变化），都应该为它的父资源 `RedisCluster` 触发一次 `Reconcile`。”

### 3. 首次 Apply 的线性过程描述

假设整个过程是单线程的，下面是详细的、线性的事件流：

**第 0 步: 用户执行命令**
*   **你**: `kubectl apply -f redis-cluster-deployment.yaml`
*   **Kubernetes API Server**: 接收请求，验证 YAML，并将新的 `RedisCluster` 对象存入 etcd。一个 "ADD" 事件产生。

---
**第 1 次 Reconcile: 添加 Finalizer**
*   **框架**: Informer 监听到 "ADD" 事件，将 `default/user-session-cache` 放入工作队列。工作线程取出该键，调用 `Reconcile`。
*   **我的 Reconcile**:
    1.  `r.Get(...)` 成功获取到 `RedisCluster` 资源。
    2.  `redisCluster.DeletionTimestamp` 为 `nil`。
    3.  `!controllerutil.ContainsFinalizer(...)` **为 true**。
    4.  进入 `if` 块，在内存中为 `redisCluster` 对象添加 Finalizer。
    5.  调用 `r.Update(ctx, redisCluster)` 将带有 Finalizer 的对象更新回 API Server。
    6.  `return ctrl.Result{}, r.Update(...)`，**第一次 Reconcile 结束**。

---
**第 2 次 Reconcile: 设置初始状态**
*   **触发**: 上一步的 `r.Update` 修改了 `RedisCluster` 对象，产生 "UPDATE" 事件。
*   **框架**: 捕获事件，再次调用 `Reconcile`。
*   **我的 Reconcile**:
    1.  `r.Get(...)` 获取到带有 Finalizer 的资源。
    2.  `!controllerutil.ContainsFinalizer(...)` **为 false**，跳过该 `if` 块。
    3.  `redisCluster.Status.Phase == ""` **为 true**。
    4.  调用 `r.updateStatus(ctx, redisCluster, PhaseCreating, 0, "")`。
    5.  `r.Status().Update(...)` 被调用，只更新 `status` 子资源。
    6.  **第二次 Reconcile 结束**（因为 `updateStatus` 不返回 `ctrl.Result`，所以代码会继续向下执行，但我们为了清晰，将每次API写操作视为一个阶段的结束）。

---
**第 3 次 Reconcile: 创建 Headless Service**
*   **触发**: 上一步的 `r.Status().Update` 产生 "UPDATE" 事件。
*   **框架**: 再次调用 `Reconcile`。
*   **我的 Reconcile**:
    1.  跳过 Finalizer 和初始状态的 `if` 块。
    2.  执行 `reconcileHeadlessService`，发现对应的 Service 不存在。
    3.  调用 `r.Create(ctx, headlessSvc)` 创建 Headless Service。
    4.  `Create` 操作成功，函数返回 `nil`。代码继续向下。

---
**第 4 次 Reconcile: 创建普通 Service**
*   **触发**: 在同一个 Reconcile 循环中继续。
*   **我的 Reconcile**:
    1.  执行 `reconcileService`，发现对应的 Service 不存在。
    2.  调用 `r.Create(ctx, svc)` 创建普通 Service。
    3.  `Create` 操作成功，函数返回 `nil`。代码继续向下。

---
**第 5 次 Reconcile: 创建 StatefulSet**
*   **触发**: 在同一个 Reconcile 循环中继续。
*   **我的 Reconcile**:
    1.  执行 `reconcileStatefulSet`，发现对应的 StatefulSet 不存在。
    2.  调用 `r.Create(ctx, sts)` 创建 StatefulSet。
    3.  `Create` 操作成功，函数返回 `nil`。代码继续向下。
    4.  执行 `updateRedisClusterStatus`，此时 Pod 列表为空，`readyReplicas` 为 0。`status` 可能没有变化，`updateStatus` 不会被调用。
    5.  **第五次 Reconcile 结束**，返回 `ctrl.Result{}, nil`。工作队列暂时为空。

---
**第 6, 7, 8... 次 Reconcile: 响应 Pod 状态变化**
*   **触发**:
    1.  **StatefulSet Controller** 检测到新的 StatefulSet，开始按顺序创建 Pod（`pod-0`, `pod-1`, `pod-2`）。
    2.  每当一个 Pod 被创建、调度、容器启动、变为 Ready，**StatefulSet Controller** 都会更新**它管理的 StatefulSet 的 `status` 字段**（例如 `readyReplicas` 从 0 -> 1 -> 2 -> 3）。
    3.  由于我们的 Controller `Owns` 这个 StatefulSet，其 `status` 的任何变化都会为**父资源 `RedisCluster`** 触发一次 `Reconcile`。
*   **框架**: 捕获 StatefulSet 的 "UPDATE" 事件，为 `RedisCluster` 调用 `Reconcile`。
*   **我的 Reconcile**:
    1.  一路执行到 `updateRedisClusterStatus`。
    2.  `r.List(...)` 获取到 Pod 列表。
    3.  计算出 `readyReplicas` 的新值（例如从 1 变为 2）。
    4.  发现 `status` 发生了变化，调用 `r.updateStatus(...)` 更新 `RedisCluster` 的状态（Phase 变为 `Updating`，然后变为 `Running`）。
    5.  这个过程会重复多次，直到所有 Pod 都 Ready，`RedisCluster` 的 `status` 达到最终的 `Running` 状态。

---
**最后一次 Reconcile: 达到稳定状态**
*   **触发**: 最后一次 `status` 更新后触发。
*   **我的 Reconcile**:
    1.  执行所有 `reconcile...` 函数，发现所有子资源都已存在且配置正确。
    2.  执行 `updateRedisClusterStatus`，发现 `status` 也没有任何变化。
    3.  返回 `ctrl.Result{}, nil`。
*   **框架**: 工作队列为空，整个系统达到稳定（静默）状态，等待下一次变更事件。

### 4. Pod 状态变更是否会触发 Reconcile？

**回答**: **会，但不是直接触发**。

*   Pod 状态的变更（Pending -> ContainerCreating -> Running -> Ready）**不会**直接触发 `RedisCluster` 的 `Reconcile`。
*   它会先触发管理这个 Pod 的 **StatefulSet Controller**。
*   StatefulSet Controller 在响应后，会更新 **StatefulSet 自己的 `.status` 字段**。
*   因为我们在 `SetupWithManager` 中设置了 `Owns(&appsv1.StatefulSet{})`，所以 StatefulSet 的状态更新会**间接触发** `RedisCluster` 的 `Reconcile`。

这是一个非常关键的、通过 Owner-Reference 实现的事件委托链。

---

## 阶段二：扩容（增加副本）

假设集群已在 3 副本状态下稳定运行，现在用户修改 `redis-cluster-deployment.yaml` 将 `replicas` 改为 4 并重新 `apply`。

1.  **触发**: `kubectl apply` 导致 `RedisCluster` 对象的 `spec.replicas` 发生变化，产生 "UPDATE" 事件。
2.  **框架**: 调用 `Reconcile`。
3.  **我的 Reconcile**:
    *   执行到 `reconcileStatefulSet`。
    *   它获取到现有的 StatefulSet（replicas=3），并生成一个期望的 StatefulSet（replicas=4）。
    *   `reflect.DeepEqual` 发现 `Replicas` 字段不一致。
    *   代码调用 `r.Update(ctx, sts)` 将 StatefulSet 的副本数更新为 4。
    *   `Reconcile` 结束。
4.  **后续**:
    *   **StatefulSet Controller** 接管，它发现期望副本数是 4 而当前是 3，于是开始创建新的 Pod `user-session-cache-3`。
    *   新 Pod 的创建和就绪过程会再次通过更新 StatefulSet 的 `status`，**触发我们 `RedisCluster` 的 Reconcile**，用于更新 `RedisCluster` 的 `status`（例如 `readyReplicas: 4`, `phase: Running`）。

---

## 阶段三：Pod 故障

假设集群在 3 副本状态下稳定运行，突然 `user-session-cache-1` 因节点故障等原因挂了。

1.  **触发**:
    *   Kubelet 上报节点或 Pod 故障。
    *   **StatefulSet Controller** 检测到 `user-session-cache-1` 不再 Ready。它会更新 **StatefulSet 的 `status`**（例如 `readyReplicas` 从 3 降为 2）。
    *   这个 `status` 变化因为 `Owns` 关系，**触发了 `RedisCluster` 的 `Reconcile`**。
2.  **框架**: 调用 `Reconcile`。
3.  **我的 Reconcile**:
    *   执行到 `updateRedisClusterStatus`。
    *   `r.List` 发现只有 2 个 Pod 是 Ready 的。
    *   它调用 `r.updateStatus` 将 `RedisCluster` 的状态更新为 `Phase: "Updating"`, `ReadyReplicas: 2`。这让用户能通过 `kubectl get rediscluster` 感知到集群的降级状态。
4.  **后续**:
    *   **StatefulSet Controller** 会尝试在其他可用节点上重新创建 `user-session-cache-1`。
    *   当新的 `user-session-cache-1` 成功启动并变为 Ready 后，StatefulSet 的 `status` 会再次更新。
    *   这会再次触发 `RedisCluster` 的 `Reconcile`，将状态恢复为 `Phase: "Running"`, `ReadyReplicas: 3`。
