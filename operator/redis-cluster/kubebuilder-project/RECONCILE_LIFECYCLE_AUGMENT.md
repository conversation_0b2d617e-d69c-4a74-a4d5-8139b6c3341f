# Redis Cluster Controller Reconcile 生命周期详细分析

## 核心结论

### Reconcile 触发次数汇总

| 场景 | 预计触发次数 | 关键触发点 |
|------|-------------|-----------|
| 首次创建（3副本） | **6-8 次** | 资源创建(5次) + Pod Ready(3次) |
| 扩容操作（3→4副本） | **2-3 次** | 配置变化(1次) + 新Pod Ready(1次) |
| Pod 异常重启 | **持续触发** | 每次Pod状态变化 |

## 阶段一：首次 kubectl apply 详细分析

### 主动触发 Reconcile 的代码位置

#### 1. 添加 Finalizer 触发（第220-224行）
```go
if !controllerutil.ContainsFinalizer(redisCluster, RedisClusterFinalizer) {
    controllerutil.AddFinalizer(redisCluster, RedisClusterFinalizer)
    // 这里会主动触发新的 Reconcile
    return ctrl.Result{}, r.Update(ctx, redisCluster)
}
```

#### 2. 创建 Headless Service 触发
```go
if err != nil && errors.IsNotFound(err) {
    headlessSvc = r.serviceForRedis(redisCluster, serviceName, true)
    // 这里会主动触发新的 Reconcile
    return r.Create(ctx, headlessSvc)
}
```

#### 3. 创建普通 Service 触发
```go
if err != nil && errors.IsNotFound(err) {
    svc = r.serviceForRedis(redisCluster, redisCluster.Name, false)
    // 这里会主动触发新的 Reconcile
    return r.Create(ctx, svc)
}
```

#### 4. 创建 StatefulSet 触发
```go
if err != nil && errors.IsNotFound(err) {
    sts = r.statefulSetForRedis(redisCluster)
    // 这里会主动触发新的 Reconcile
    return r.Create(ctx, sts)
}
```

#### 5. 不会触发 Reconcile 的操作
```go
// 这个操作不会触发新的 Reconcile
r.Status().Update(ctx, redisCluster)
// 原因：只更新 status 子资源，controller-runtime 默认不监听 status 变化
```

### 详细代码执行流程（MaxConcurrentReconciles: 1, 副本数: 3）

#### T0: kubectl apply -f redis-cluster-deployment.yaml
```
用户操作 → API Server 存储到 etcd → 触发 Controller
```

#### T1: 【第1次 Reconcile】- 初始创建
```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 第115行：获取 RedisCluster 资源
    redisCluster := &dbv1.RedisCluster{}
    err := r.Get(ctx, req.NamespacedName, redisCluster) // ✓ 成功获取

    // 第139行：验证配置
    if err := r.validateRedisCluster(redisCluster); err != nil { // ✓ 验证通过

    // 第178行：检查删除时间戳
    if redisCluster.DeletionTimestamp != nil { // ✓ 为 nil，跳过删除逻辑

    // 第218行：检查 Finalizer
    if !controllerutil.ContainsFinalizer(redisCluster, RedisClusterFinalizer) { // ✓ 没有 Finalizer
        controllerutil.AddFinalizer(redisCluster, RedisClusterFinalizer)
        return ctrl.Result{}, r.Update(ctx, redisCluster) // 🔥 主动触发新 Reconcile
    }
    // 后续代码不会执行，因为已经 return
}
```
**结果**: 添加 Finalizer，触发第2次 Reconcile

#### T2: 【第2次 Reconcile】- Finalizer 已添加
```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 前面步骤相同...

    // 第218行：检查 Finalizer
    if !controllerutil.ContainsFinalizer(redisCluster, RedisClusterFinalizer) { // ✗ 已有 Finalizer，跳过

    // 第226行：设置初始状态
    if redisCluster.Status.Phase == "" { // ✓ 状态为空
        r.updateStatus(ctx, redisCluster, PhaseCreating, 0, "") // 不触发 Reconcile
    }

    // 第232行：协调 Headless Service
    if err := r.reconcileHeadlessService(ctx, redisCluster, headlessSvcName); err != nil {
        // 进入 reconcileHeadlessService 方法：
        headlessSvc := &corev1.Service{}
        err := r.Get(ctx, types.NamespacedName{...}, headlessSvc) // 返回 NotFound

        if err != nil && errors.IsNotFound(err) { // ✓ Service 不存在
            headlessSvc = r.serviceForRedis(redisCluster, serviceName, true)
            return r.Create(ctx, headlessSvc) // 🔥 主动触发新 Reconcile
        }
    }
    // 后续代码不会执行
}
```
**结果**: 创建 Headless Service，触发第3次 Reconcile

#### T3: 【第3次 Reconcile】- Headless Service 已创建
```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 前面步骤都跳过...

    // 第232行：协调 Headless Service
    // reconcileHeadlessService 检查发现 Service 已存在，返回 nil

    // 第237行：协调普通 Service
    if err := r.reconcileService(ctx, redisCluster); err != nil {
        // 进入 reconcileService 方法：
        svc := &corev1.Service{}
        err := r.Get(ctx, types.NamespacedName{...}, svc) // 返回 NotFound

        if err != nil && errors.IsNotFound(err) { // ✓ Service 不存在
            svc = r.serviceForRedis(redisCluster, redisCluster.Name, false)
            return r.Create(ctx, svc) // 🔥 主动触发新 Reconcile
        }
    }
}
```
**结果**: 创建普通 Service，触发第4次 Reconcile

#### T4: 【第4次 Reconcile】- 普通 Service 已创建
```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 前面步骤都跳过...

    // 第242行：协调 StatefulSet
    if err := r.reconcileStatefulSet(ctx, redisCluster); err != nil {
        // 进入 reconcileStatefulSet 方法：
        sts := &appsv1.StatefulSet{}
        err := r.Get(ctx, types.NamespacedName{...}, sts) // 返回 NotFound

        if err != nil && errors.IsNotFound(err) { // ✓ StatefulSet 不存在
            sts = r.statefulSetForRedis(redisCluster)
            return r.Create(ctx, sts) // 🔥 主动触发新 Reconcile
        }
    }
}
```
**结果**: 创建 StatefulSet，触发第5次 Reconcile

#### T5: 【第5次 Reconcile】- StatefulSet 已创建
```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 前面步骤都跳过...

    // 第247行：更新 RedisCluster 状态
    if err := r.updateRedisClusterStatus(ctx, redisCluster); err != nil {
        // 进入 updateRedisClusterStatus 方法：

        // 第526行：获取 Pod 列表
        podList := &corev1.PodList{}
        if err := r.List(ctx, podList, listOpts...); err != nil { // ✓ 成功，但 Pod 列表可能为空

        // 第540行：计算就绪副本数
        var readyReplicas int32 = 0 // 没有 Ready 的 Pod

        // 第554行：确定阶段
        phase := PhaseCreating // 因为 readyReplicas == 0

        // 第563行：检查状态是否变化
        if redisCluster.Status.ReadyReplicas != readyReplicas || ... { // ✓ 状态发生变化
            return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
            // 这里调用 r.Status().Update()，不会触发新的 Reconcile
        }
    }

    // 第251行：正常结束
    return ctrl.Result{}, nil
}
```
**结果**: 更新状态为 Creating，等待 Pod 创建（不触发新 Reconcile）

#### T6: Kubernetes 内部处理
```
StatefulSet Controller 检测到新的 StatefulSet
    ↓
创建第一个 Pod: redis-sample-0
    ↓
Scheduler 分配节点
    ↓
Kubelet 开始拉取镜像、创建容器
```

#### T7: 【第6次 Reconcile】- 第一个 Pod 变为 Ready
```
触发原因: Pod 状态变化 (通过 OwnerReference 触发)
```

```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 前面步骤都跳过...

    // 第247行：更新 RedisCluster 状态
    if err := r.updateRedisClusterStatus(ctx, redisCluster); err != nil {

        // 第526行：获取 Pod 列表
        podList := &corev1.PodList{} // 现在包含 redis-sample-0 (Ready)

        // 第540行：计算就绪副本数
        for _, pod := range podList.Items {
            if isPodReady(&pod) { // redis-sample-0 返回 true
                readyReplicas++ // readyReplicas = 1
                if masterPod == "" {
                    masterPod = pod.Name // masterPod = "redis-sample-0"
                }
            }
        }

        // 第554行：确定阶段
        phase := PhaseRunning // 默认
        if readyReplicas == 0 {
            phase = PhaseCreating
        } else if redisCluster.Spec.Replicas != nil && readyReplicas < *redisCluster.Spec.Replicas {
            phase = PhaseUpdating // ✓ 因为 1 < 3
        }

        // 第563行：检查状态是否变化
        // 之前: ReadyReplicas=0, Phase=Creating, Master=""
        // 现在: readyReplicas=1, phase=Updating, masterPod="redis-sample-0"
        if redisCluster.Status.ReadyReplicas != readyReplicas || // ✓ 0 != 1
           redisCluster.Status.Phase != phase ||                // ✓ Creating != Updating
           redisCluster.Status.Master != masterPod {            // ✓ "" != "redis-sample-0"
            return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
        }
    }

    return ctrl.Result{}, nil
}
```
**结果**: 状态更新为 readyReplicas=1, phase=Updating, master="redis-sample-0"

#### T8: 【第7次 Reconcile】- 第二个 Pod 变为 Ready
```
触发原因: redis-sample-1 状态变化为 Ready
```

```go
// 类似的执行流程...
// 第540行：计算就绪副本数
readyReplicas = 2 // 现在有 2 个 Ready 的 Pod
phase = PhaseUpdating // 仍然是 Updating，因为 2 < 3

// 第563行：状态变化检查
// 之前: ReadyReplicas=1
// 现在: readyReplicas=2
if redisCluster.Status.ReadyReplicas != readyReplicas { // ✓ 1 != 2
    return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
}
```
**结果**: 状态更新为 readyReplicas=2, phase=Updating

#### T9: 【第8次 Reconcile】- 第三个 Pod 变为 Ready
```
触发原因: redis-sample-2 状态变化为 Ready
```

```go
// 第540行：计算就绪副本数
readyReplicas = 3 // 现在有 3 个 Ready 的 Pod

// 第554行：确定阶段
phase := PhaseRunning // 默认
if readyReplicas == 0 {
    phase = PhaseCreating
} else if redisCluster.Spec.Replicas != nil && readyReplicas < *redisCluster.Spec.Replicas {
    phase = PhaseUpdating // ✗ 因为 3 == 3，不进入此分支
}
// phase 保持为 PhaseRunning

// 第563行：状态变化检查
// 之前: ReadyReplicas=2, Phase=Updating
// 现在: readyReplicas=3, phase=Running
if redisCluster.Status.ReadyReplicas != readyReplicas || // ✓ 2 != 3
   redisCluster.Status.Phase != phase {                 // ✓ Updating != Running
    return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
}
```
**结果**: 状态更新为 readyReplicas=3, phase=Running（最终稳定状态）

#### T10: 稳定状态 - 后续 Reconcile
```go
// 任何后续的 Reconcile 都会执行到：
if redisCluster.Status.ReadyReplicas != readyReplicas || // ✗ 3 == 3
   redisCluster.Status.Phase != phase ||                // ✗ Running == Running
   redisCluster.Status.Master != masterPod {            // ✗ 相同
    // 不会进入此分支
}
return nil // 直接返回，无任何操作
```
**结果**: 无状态变化，不执行任何操作

### 各组件职责分工

#### Kubernetes 做了什么
1. **API Server**: 资源验证、存储、事件通知
2. **Controller Manager**: 资源监听、事件分发
3. **Scheduler**: Pod 节点分配
4. **Kubelet**: Pod 生命周期管理、状态报告

#### Kubebuilder 框架做了什么
1. **资源监听设置**:
   ```go
   For(&dbv1.RedisCluster{}).           // 主资源监听
   Owns(&appsv1.StatefulSet{}).         // 子资源监听
   Owns(&corev1.Service{}).             // 子资源监听
   ```

2. **并发控制**:
   ```go
   MaxConcurrentReconciles: 3           // 最多3个并发
   ```

3. **OwnerReference 自动管理**: 实现级联删除和事件关联

#### 我们的 Reconcile 做了什么

**触发 Reconcile 的操作**:
```go
// 会触发新 Reconcile 的操作
r.Update(ctx, redisCluster)              // 更新主资源
r.Create(ctx, headlessSvc)               // 创建子资源
r.Create(ctx, svc)                       // 创建子资源
r.Create(ctx, sts)                       // 创建子资源

// 不会触发 Reconcile 的操作
r.Status().Update(ctx, redisCluster)     // 仅状态更新
```

**每次 Reconcile 的核心逻辑**:
1. 获取当前资源状态
2. 与期望状态比较
3. 执行必要的创建/更新操作
4. 更新状态信息

## 阶段二：扩容场景详细分析

### 扩容操作流程（3→4副本）

#### T0: 修改配置并应用
```bash
# 修改 redis-cluster-deployment.yaml: replicas: 3 → 4
kubectl apply -f redis-cluster-deployment.yaml
```

#### T1: 【第1次 Reconcile】- 检测配置变化
```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 前面步骤都跳过...

    // 第242行：协调 StatefulSet
    if err := r.reconcileStatefulSet(ctx, redisCluster); err != nil {

        // 获取当前 StatefulSet
        sts := &appsv1.StatefulSet{}
        err := r.Get(ctx, types.NamespacedName{...}, sts) // ✓ StatefulSet 存在

        // 生成期望的 StatefulSet 配置
        desired := r.statefulSetForRedis(redisCluster) // replicas = 4

        // 第484行：检查是否需要更新
        if !reflect.DeepEqual(sts.Spec.Replicas, desired.Spec.Replicas) || // ✓ 3 != 4
           !reflect.DeepEqual(sts.Spec.Template.Spec.Containers, desired.Spec.Template.Spec.Containers) ||
           !reflect.DeepEqual(sts.Spec.VolumeClaimTemplates, desired.Spec.VolumeClaimTemplates) {

            // 第489行：更新 StatefulSet 配置
            sts.Spec.Replicas = desired.Spec.Replicas // 3 → 4
            sts.Spec.Template = desired.Spec.Template
            sts.Spec.VolumeClaimTemplates = desired.Spec.VolumeClaimTemplates
            return r.Update(ctx, sts) // 🔥 主动触发新 Reconcile
        }
    }
}
```
**结果**: 更新 StatefulSet replicas: 3→4，触发第2次 Reconcile

#### T2: 【第2次 Reconcile】- StatefulSet 已更新
```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 前面步骤都跳过...

    // 第242行：协调 StatefulSet
    // reconcileStatefulSet 检查发现配置已一致，无需更新

    // 第247行：更新 RedisCluster 状态
    if err := r.updateRedisClusterStatus(ctx, redisCluster); err != nil {

        // 第540行：计算就绪副本数
        readyReplicas = 3 // 新 Pod 还未 Ready

        // 第554行：确定阶段
        // redisCluster.Spec.Replicas = 4, readyReplicas = 3
        if readyReplicas < *redisCluster.Spec.Replicas { // ✓ 3 < 4
            phase = PhaseUpdating
        }

        // 第563行：状态变化检查
        // 之前: Phase=Running
        // 现在: phase=Updating
        if redisCluster.Status.Phase != phase { // ✓ Running != Updating
            return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
        }
    }
}
```
**结果**: 状态更新为 phase=Updating，等待新 Pod Ready

#### T3: Kubernetes 创建新 Pod
```
StatefulSet Controller 检测到 replicas 变化
    ↓
创建新 Pod: redis-sample-3
    ↓
Pod 经历: Pending → ContainerCreating → Running → Ready
```

#### T4: 【第3次 Reconcile】- 新 Pod 变为 Ready
```go
// 第540行：计算就绪副本数
readyReplicas = 4 // 现在有 4 个 Ready 的 Pod

// 第554行：确定阶段
if readyReplicas < *redisCluster.Spec.Replicas { // ✗ 4 == 4，不进入
}
phase = PhaseRunning // 保持默认值

// 第563行：状态变化检查
// 之前: ReadyReplicas=3, Phase=Updating
// 现在: readyReplicas=4, phase=Running
if redisCluster.Status.ReadyReplicas != readyReplicas || // ✓ 3 != 4
   redisCluster.Status.Phase != phase {                 // ✓ Updating != Running
    return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
}
```
**结果**: 扩容完成，状态更新为 readyReplicas=4, phase=Running

## 阶段三：Pod 异常场景详细分析

### 场景：redis-sample-1 Pod 异常重启

#### T0: 正常运行状态
```
当前状态: readyReplicas=3, phase=Running
所有 Pod: redis-sample-0 (Ready), redis-sample-1 (Ready), redis-sample-2 (Ready)
```

#### T1: 【第1次 Reconcile】- 检测到 Pod 异常
```
触发原因: redis-sample-1 状态从 Ready 变为 Failed (如 OOMKilled)
```

```go
func (r *RedisClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 前面步骤都跳过...

    // 第247行：更新 RedisCluster 状态
    if err := r.updateRedisClusterStatus(ctx, redisCluster); err != nil {

        // 第540行：计算就绪副本数
        for _, pod := range podList.Items {
            if isPodReady(&pod) {
                // redis-sample-0: Ready ✓
                // redis-sample-1: Failed ✗ (isPodReady 返回 false)
                // redis-sample-2: Ready ✓
                readyReplicas++ // readyReplicas = 2
            }
        }

        // 第554行：确定阶段
        if readyReplicas < *redisCluster.Spec.Replicas { // ✓ 2 < 3
            phase = PhaseUpdating
        }

        // 第563行：状态变化检查
        // 之前: ReadyReplicas=3, Phase=Running
        // 现在: readyReplicas=2, phase=Updating
        if redisCluster.Status.ReadyReplicas != readyReplicas || // ✓ 3 != 2
           redisCluster.Status.Phase != phase {                 // ✓ Running != Updating
            return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
        }
    }
}
```
**结果**: 状态更新为 readyReplicas=2, phase=Updating

#### T2: Kubernetes 自动重启 Pod
```
Kubelet 检测到 Pod 失败
    ↓
重启容器或重新创建 Pod
    ↓
Pod 状态: Failed → Pending → ContainerCreating
```

#### T3: 【第2次 Reconcile】- Pod 重启中
```
触发原因: Pod 状态变化 (Failed → Pending)
```

```go
// 第540行：计算就绪副本数
for _, pod := range podList.Items {
    if isPodReady(&pod) {
        // redis-sample-0: Ready ✓
        // redis-sample-1: Pending ✗ (isPodReady 返回 false)
        // redis-sample-2: Ready ✓
        readyReplicas++ // readyReplicas = 2 (无变化)
    }
}

// 第554行：确定阶段
phase = PhaseUpdating // 仍然是 Updating

// 第563行：状态变化检查
// 之前: ReadyReplicas=2, Phase=Updating
// 现在: readyReplicas=2, phase=Updating
if redisCluster.Status.ReadyReplicas != readyReplicas || // ✗ 2 == 2
   redisCluster.Status.Phase != phase {                 // ✗ Updating == Updating
    // 不进入此分支
}
return nil // 无状态变化，直接返回
```
**结果**: 无状态变化，无操作

#### T4: 【第3次 Reconcile】- Pod 恢复正常
```
触发原因: redis-sample-1 状态变为 Ready
```

```go
// 第540行：计算就绪副本数
for _, pod := range podList.Items {
    if isPodReady(&pod) {
        // redis-sample-0: Ready ✓
        // redis-sample-1: Ready ✓ (已恢复)
        // redis-sample-2: Ready ✓
        readyReplicas++ // readyReplicas = 3
    }
}

// 第554行：确定阶段
if readyReplicas < *redisCluster.Spec.Replicas { // ✗ 3 == 3，不进入
}
phase = PhaseRunning // 恢复为 Running

// 第563行：状态变化检查
// 之前: ReadyReplicas=2, Phase=Updating
// 现在: readyReplicas=3, phase=Running
if redisCluster.Status.ReadyReplicas != readyReplicas || // ✓ 2 != 3
   redisCluster.Status.Phase != phase {                 // ✓ Updating != Running
    return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
}
```
**结果**: 恢复正常状态，readyReplicas=3, phase=Running

### 持续异常场景

如果 Pod 持续无法启动（如镜像拉取失败），会出现：

```
Pod 状态循环: Failed → Pending → ContainerCreating → ImagePullBackOff → Failed
    ↓
每次状态变化都触发 Reconcile
    ↓
但 readyReplicas 始终为 2，phase 始终为 Updating
    ↓
大部分 Reconcile 都会在状态检查时直接返回 nil（无变化）
```

**关键点**: 只有当 `readyReplicas` 数量真正发生变化时，才会执行状态更新操作。

## 关键设计原则

### 1. 幂等性保证
每次 Reconcile 都能安全重复执行，不会产生副作用。

### 2. 状态驱动架构
基于"当前状态 vs 期望状态"的差异进行操作：
```go
// 检查差异的典型模式
if !reflect.DeepEqual(current.Spec, desired.Spec) {
    // 执行更新操作
    return r.Update(ctx, current)
}
```

### 3. 最小化触发策略
只在真正需要时才执行更新操作，避免无限循环：
```go
// 只有状态真正变化时才更新
if redisCluster.Status.ReadyReplicas != readyReplicas {
    return r.updateStatus(ctx, redisCluster, phase, readyReplicas, masterPod)
}
```

### 4. 分阶段处理
每次 Reconcile 只处理一个主要任务，通过多次循环完成复杂操作。

## 性能优化建议

1. **减少不必要的 API 调用**: 更新前先检查是否真的需要更新
2. **批量状态更新**: 合并多个状态变化为一次更新
3. **智能重试机制**: 使用指数退避替代固定间隔
4. **事件记录**: 添加 Kubernetes Events 便于调试

## 监控指标建议

1. **Reconcile 频率**: 监控每个资源的 Reconcile 触发频率
2. **Reconcile 耗时**: 监控每次 Reconcile 的执行时间
3. **错误率**: 监控 Reconcile 失败的比例
4. **队列深度**: 监控工作队列的积压情况

## 核心总结

### 主动触发 Reconcile 的代码位置（5处）

1. **第220-224行**: 添加 Finalizer
   ```go
   return ctrl.Result{}, r.Update(ctx, redisCluster)
   ```

2. **reconcileHeadlessService 方法**: 创建 Headless Service
   ```go
   return r.Create(ctx, headlessSvc)
   ```

3. **reconcileService 方法**: 创建普通 Service
   ```go
   return r.Create(ctx, svc)
   ```

4. **reconcileStatefulSet 方法**: 创建 StatefulSet
   ```go
   return r.Create(ctx, sts)
   ```

5. **reconcileStatefulSet 方法**: 更新 StatefulSet（扩容时）
   ```go
   return r.Update(ctx, sts)
   ```

### 不会触发 Reconcile 的操作

```go
// 只更新 status 子资源，不会触发新的 Reconcile
r.Status().Update(ctx, redisCluster)
```

### Reconcile 触发次数统计

| 场景 | 主动触发 | 被动触发 | 总计 | 备注 |
|------|---------|---------|------|------|
| 首次创建（3副本） | 5次 | 3次 | **8次** | 5次资源创建 + 3次Pod Ready |
| 扩容（3→4副本） | 1次 | 1次 | **2次** | 1次StatefulSet更新 + 1次新Pod Ready |
| Pod异常重启 | 0次 | 2次 | **2次** | Pod失败→恢复的状态变化 |

### MaxConcurrentReconciles: 1 的影响

- **严格串行执行**: 每次 Reconcile 完全结束后才开始下一次
- **状态一致性**: 每次都能看到前一次操作的完整结果
- **可预测性**: 流程更加线性和可控
- **调试友好**: 更容易追踪问题和理解执行顺序

### 关键设计原则验证

1. **幂等性**: ✅ 每次 Reconcile 都可以安全重复执行
2. **状态驱动**: ✅ 基于当前状态与期望状态的差异操作
3. **最小化触发**: ✅ 只在状态真正变化时才更新
4. **分阶段处理**: ✅ 每次只处理一个主要任务后返回
