# Build the manager binary
FROM golang:1.21 AS builder
ARG TARGETOS
ARG TARGETARCH

WORKDIR /workspace
# Copy the Go Modules manifests
COPY go.mod go.mod
COPY go.sum go.sum
# cache deps before building and copying source so that we don't need to re-download as much
# and so that source changes don't invalidate our downloaded layer
RUN go mod download

# Copy the go source
COPY cmd/main.go cmd/main.go
COPY api/ api/
COPY internal/controller/ internal/controller/

# Build
# the GOARCH has not a default value to allow the binary be built according to the host where the command
# was called. For example, if we call make docker-build in a local env which has the Apple Silicon M1 SO
# the docker BUILDPLATFORM arg will be linux/arm64 when for Apple x86 it will be linux/amd64. Therefore,
# by leaving it empty we can ensure that the container and binary shipped on it will have the same platform.
RUN CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH} go build -a -o manager cmd/main.go

# Use distroless as minimal base image to package the manager binary
# Refer to https://github.com/GoogleContainerTools/distroless for more details
# 这是一个多阶段构建（Multi-stage build）的 Dockerfile
# 可以在一个 Dockerfile 中使用多个 FROM 指令，每个 FROM 开始一个新的构建阶段

# 第一个阶段 (builder): 用于编译 Go 代码
# - 使用完整的 golang:1.21 镜像，包含 Go 编译器和构建工具
# - 下载依赖、编译源码，生成二进制文件
# - 这个阶段会产生一个较大的镜像（包含 Go 工具链）

# 第二个阶段: 用于运行时环境
# - 使用精简的 distroless 镜像作为基础镜像
# - 只复制第一阶段编译好的二进制文件
# - 最终镜像非常小，只包含运行时必需的文件

# 多阶段构建的优势：
# 1. 减小最终镜像大小：构建工具不会包含在最终镜像中
# 2. 提高安全性：运行时镜像不包含编译器等开发工具
# 3. 优化构建缓存：可以单独缓存各个阶段
# 📝 多阶段构建详解：
# 
# ❓ 问题1：最后 build -t 产生几个镜像？
# 答：只产生 1 个最终镜像！
# - 虽然有 2 个 FROM 指令，但只有最后一个 FROM 定义的阶段会成为最终镜像
# - 第一个阶段（builder）只是中间构建阶段，不会产生独立的镜像
# - 执行 `docker build -t redis-operator:latest .` 只会得到一个镜像
#
# ❓ 问题2：实际的启动命令是什么？
# 答：ENTRYPOINT ["/manager"]
# - 容器启动时会执行 /manager 二进制文件
# - 这个文件是从 builder 阶段复制过来的编译好的 Go 程序
# - 等价于在容器内执行：./manager
#
# ❓ 问题3：两个 FROM 基础镜像数据会乱掉吗？
# 答：不会！每个阶段都是独立的文件系统
# - 第一阶段（golang:1.21）：用于编译，有完整的 Go 工具链
# - 第二阶段（distroless/static）：从零开始的新文件系统
# - 通过 COPY --from=builder 显式地从第一阶段复制需要的文件
# - 两个阶段完全隔离，最终镜像只包含第二阶段的内容
#
# 🔄 构建流程示例：
# 1. docker build -t redis-operator:latest .
# 2. Docker 创建第一个临时容器（基于 golang:1.21）
# 3. 在第一个容器中执行编译操作，生成 /workspace/manager
# 4. Docker 创建第二个容器（基于 distroless/static:nonroot）
# 5. 将第一个容器的 /workspace/manager 复制到第二个容器的 /manager
# 6. 第一个容器被丢弃，只保留第二个容器作为最终镜像
# 7. 最终镜像大小：约 20MB（vs 单阶段构建的 800MB+）

FROM gcr.io/distroless/static:nonroot
WORKDIR /
COPY --from=builder /workspace/manager .
USER 65532:65532

# 🚀 ENTRYPOINT 详解：容器启动的入口点
#
# ❓ ENTRYPOINT 是什么意思？
# 答：ENTRYPOINT 定义了容器启动时必须执行的命令，类似于程序的"main函数"
# - 它不是"接口"的概念，而是"程序入口点"的概念
# - 当你运行 `docker run redis-operator` 时，实际执行的就是 ENTRYPOINT 指定的命令
#
# 🔍 ENTRYPOINT vs RUN 的区别：
# - RUN：在构建镜像时执行（build time），用于安装软件、编译代码等
# - ENTRYPOINT：在运行容器时执行（runtime），定义容器的主进程
#
# 📋 实际执行流程：
# 1. 开发阶段：你在本地运行 `go run main.go` 或 `make run` 
# 2. 构建阶段：Dockerfile 中的 RUN 指令编译生成二进制文件 `/manager`
# 3. 运行阶段：容器启动时执行 ENTRYPOINT ["/manager"]
#
# 🎯 为什么不是 `go run`？
# 因为最终镜像使用的是 distroless，它是一个极简镜像：
# - 没有 shell（/bin/sh）
# - 没有 Go 运行时环境
# - 没有包管理器
# - 只有一个编译好的二进制文件和最基本的系统库
#
# 🔄 从开发到部署的完整流程：
# 开发时：make run          → go run ./cmd/main.go
# 构建时：docker build      → go build -o manager ./cmd/main.go  
# 部署时：kubectl apply     → 容器运行 /manager
# 运维时：docker run        → 直接执行二进制文件
#
# 💡 这就是为什么云原生应用要编译成二进制：
# - 启动速度快（毫秒级 vs 秒级）
# - 镜像体积小（20MB vs 800MB+）
# - 安全性高（无额外工具链）
# - 资源消耗低（无解释器开销）

ENTRYPOINT ["/manager"]
