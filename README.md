
# Kubernetes Learning and Practice

This repository contains configurations and examples for learning and practicing Kubernetes.

## Minikube Setup

To start a multi-node Minikube cluster:

```bash
minikube start --profile=multinode --nodes=2 --memory=8g --cpus=4
```

Check Minikube profiles:

```bash
minikube profile list
```

Switch to the `multinode` profile:

```bash
minikube profile multinode
```

Verify the active profile:

```bash
minikube profile list
```

## Kubernetes Namespace Configuration

Create a new namespace for applications:

```bash
kubectl create namespace app-namespace
```

Set the current context to use the new namespace:

```bash
kubectl config set-context --current --namespace=app-namespace
```

## Load Docker Images into Minikube

Load local Docker images into the Minikube cluster's image cache:

```bash
minikube image load simple-python-app-image:v1
minikube image load simple-python-app-image:v2
minikube image load busybox:1.36.1
minikube image load grafana/grafana:11.0.0
minikube start --profile=multinode --nodes=2 --memory=8g --cpus=4
 minikube profile list
 minikube profile multinode
 minikube profile list  


kubectl create namespace app-namespace
kubectl config set-context --current --namespace=app-namespace




minikube image load simple-python-app-image:v1
minikube image load simple-python-app-image:v2
minikube image load busybox:1.36.1



minikube image load grafana/grafana:11.0.0
```
